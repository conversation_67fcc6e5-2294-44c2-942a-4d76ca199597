<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.digiwin.escloud.aioitms.networksecurity.dao.NetworkSecurityExamMapper">

  <select id="selectExamIdByCode" resultType="Long">
        SELECT id FROM aiops_exam
        WHERE code = #{code}
        LIMIT 1
    </select>

    <!-- 分页查询exam_record -->
     <select id="selectExamRecordsByExamId" resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExamRecord">
        SELECT * FROM aiops_exam_record
        WHERE aeId IN
        <foreach item="aeId" collection="aeIdList" open="(" separator="," close=")">
            #{aeId}
        </foreach>
        <if test="userName != null and userName != ''">
            AND userName LIKE CONCAT('%', #{userName}, '%')
        </if>
         <if test="eid != null">
             AND eid = #{eid}
         </if>
        <if test="examStartTimeStart != null and examStartTimeStart != ''">
            AND examStartTime &gt;= #{examStartTimeStart}
        </if>
        <if test="examStartTimeEnd != null and examStartTimeEnd != ''">
            AND examStartTime &lt;= #{examStartTimeEnd}
        </if>
        <if test="examTitle != null and examTitle != ''">
            AND examTitle LIKE CONCAT('%', #{examTitle}, '%')
        </if>
        ORDER BY createDate DESC
    </select>


    <resultMap id="BaseResultMap" type="com.digiwin.escloud.aioitms.exam.model.AiopsExamRecordsReportRecord">
        <id column="id" property="id" />
        <result column="sid" property="sid" />
        <result column="eid" property="eid" />
        <result column="aerId" property="aerId" />
        <result column="serviceCode" property="serviceCode" />
        <result column="customerName" property="customerName" />
        <result column="customerFullName" property="customerFullName" />
        <result column="reportStatus" property="reportStatus" />
        <result column="userSid" property="userSid" />
        <result column="userName" property="userName" />
        <result column="reportDate" property="reportDate" />
        <result column="generationTime" property="generationTime" />
        <result column="createTime" property="createTime" />
        <result column="updateTime" property="updateTime" />
    </resultMap>

    <select id="selectReportByAerIds" resultMap="BaseResultMap">
        SELECT *
        FROM aiops_exam_records_report_records
        WHERE aerId IN
        <foreach item="aerId" collection="aerIdList" open="(" separator="," close=")">
            #{aerId}
        </foreach>
        ORDER BY createTime DESC
    </select>


    <insert id="insertOrUpdateProjectType" parameterType="com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType">
        INSERT INTO network_security_examination_project_type (id, parentCode, categoryName, categoryCode, modelCode, modelName, createTime, updateTime)
        VALUES (#{id}, #{parentCode}, #{categoryName}, #{categoryCode}, #{modelCode}, #{modelName}, #{createTime}, #{updateTime})
        ON DUPLICATE KEY UPDATE
                             parentCode = #{parentCode},
                             categoryName = #{categoryName},
                             categoryCode = #{categoryCode},
                             modelCode = #{modelCode},
                             modelName = #{modelName},
                             updateTime = #{updateTime}
    </insert>

    <select id="selectProjectType" resultType="com.digiwin.escloud.aioitms.networksecurity.model.NetworkSecurityExaminationProjectType">
        SELECT * FROM network_security_examination_project_type
        WHERE 1=1
        <if test="categoryCode != null and categoryCode != ''">
            AND categoryCode = #{categoryCode}
        </if>
        <if test="categoryName != null and categoryName != ''">
            AND categoryName LIKE CONCAT('%', #{categoryName}, '%')
        </if>
        <if test="parentCode != null and parentCode != ''">
            AND parentCode = #{parentCode}
        </if>
        <if test="filterNullModel != null and filterNullModel">
            AND modelCode IS NOT NULL AND modelCode != ''
        </if>
        <if test="modelCode != null and modelCode != ''">
            AND modelCode = #{modelCode}
        </if>
    </select>

    <delete id="deleteByAiopsItemIdAndAerId">
        DELETE FROM aiops_exam_item_instance_score
        WHERE aiopsItemId = #{aiopsItemId} AND aerId = #{aerId}
    </delete>

    <select id="selectExamItemInstanceScore"
            resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore">
        SELECT id, aerId, aiopsItem, aiopsItemId, examComplete, examScore, scoreTime, levelCode,
               examInstanceStatus, networkExamCategoryCode, networkExamAssetId, createDate, updateDate, deviceName
        FROM aiops_exam_item_instance_score
        WHERE aerId = #{aerId}
    </select>

    <select id="selectExamIdByCodes" resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExam">
        SELECT code, id
        FROM aiops_exam
        WHERE code IN
        <foreach collection="codes" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
    </select>

    <select id="selectExamItemInstanceScoreTotal"
            resultType="com.digiwin.escloud.aioitms.exam.model.AiopsExamItemInstanceScore">
        SELECT aeiis.networkExamCategoryCode, aeiis.aiopsItem, count(aeiis.aiopsItem) AS aiopsItemTotal
        FROM aiops_exam_item_instance_score aeiis
        WHERE aerId = #{aerId}
        GROUP BY aiopsItem
    </select>

    <!-- 查询code=100的设备名称 -->
    <select id="queryDeviceNamesByCode100" resultType="java.util.Map">
        SELECT deviceName,'100' as aiopsItem
        FROM (
            SELECT
                ad.deviceName,
                MAX(adcd.__version__) AS latest_version
            FROM aiops_device_collect_detail adcd
            LEFT JOIN aiops_device_collect adc ON adcd.adcId = adc.id
            LEFT JOIN aiops_device ad ON ad.deviceId = adc.deviceId
            WHERE
                adcd.accId IN (102000000002003)
                AND ad.eid = #{eid}
                AND adcd.isEnable = true
                AND ad.deviceName IS NOT NULL
            GROUP BY ad.deviceName
        ) AS grouped_devices
        ORDER BY latest_version DESC
        LIMIT 10
    </select>

    <!-- 查询code=08的设备名称 -->
    <select id="queryDeviceNamesByCode08" resultType="java.util.Map">
        SELECT deviceName,'08' as aiopsItem
        FROM (
            SELECT
                ad.deviceName,
                MAX(adcd.__version__) AS latest_version
            FROM aiops_device_collect_detail adcd
            LEFT JOIN aiops_device_collect adc ON adcd.adcId = adc.id
            LEFT JOIN aiops_device ad ON ad.deviceId = adc.deviceId
            WHERE
                adcd.accId IN (548800829669952,
                               548795493921344,
                               550557677097536,
                               550563516092992,
                               550562617242176)
                AND ad.eid = #{eid}
                AND adcd.isEnable = true
                AND ad.deviceName IS NOT NULL
            GROUP BY ad.deviceName
        ) AS grouped_devices
        ORDER BY latest_version DESC
        LIMIT 10
    </select>

</mapper>
