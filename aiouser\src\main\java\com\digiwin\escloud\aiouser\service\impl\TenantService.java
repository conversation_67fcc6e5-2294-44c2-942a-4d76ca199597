package com.digiwin.escloud.aiouser.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.digiwin.escloud.aioitms.model.device.TenantDeviceCnt;
import com.digiwin.escloud.aioitms.model.device.TenantDeviceCntReq;
import com.digiwin.escloud.aiouser.cache.ProductCache;
import com.digiwin.escloud.aiouser.cache.TenantCache;
import com.digiwin.escloud.aiouser.constant.AioUserConst;
import com.digiwin.escloud.aiouser.dao.*;
import com.digiwin.escloud.aiouser.model.common.InviteType;
import com.digiwin.escloud.aiouser.model.module.ModuleContractDetailBase;
import com.digiwin.escloud.aiouser.model.module.ModuleQryReq;
import com.digiwin.escloud.aiouser.model.supplier.*;
import com.digiwin.escloud.aiouser.model.tenant.*;
import com.digiwin.escloud.aiouser.model.tenantNotice.NotifyModuleEnum;
import com.digiwin.escloud.aiouser.model.tenantNotice.UserNotifySettingResultRes;
import com.digiwin.escloud.aiouser.model.user.AuthoredUser;
import com.digiwin.escloud.aiouser.model.user.*;
import com.digiwin.escloud.aiouser.mybatisenums.OperateType;
import com.digiwin.escloud.aiouser.service.*;
import com.digiwin.escloud.aiouser.util.IamUtils;
import com.digiwin.escloud.common.asiainfo.AsiaInfoResponse;
import com.digiwin.escloud.common.constant.AioPlatformEnum;
import com.digiwin.escloud.common.controller.ParamCheckHelp;
import com.digiwin.escloud.common.feign.AioItmsFeignClient;
import com.digiwin.escloud.common.feign.UserV2FeignClient;
import com.digiwin.escloud.common.model.ResponseBase;
import com.digiwin.escloud.common.model.ResponseCode;
import com.digiwin.escloud.common.model.TenantTpParams;
import com.digiwin.escloud.common.response.BaseResponse;
import com.digiwin.escloud.common.response.PageInfo;
import com.digiwin.escloud.common.util.*;
import com.digiwin.escloud.integration.api.iam.req.user.*;
import com.digiwin.escloud.integration.service.CacService;
import com.digiwin.escloud.integration.service.IamService;
import com.digiwin.escloud.integration.service.PmcService;
import com.digiwin.escloud.integration.service.iam.PermissionCopyService;
import com.digiwin.escloud.integration.service.iam.PermissionService;
import com.digiwin.escloud.integration.service.iam.common.RoleType;
import com.digiwin.escloud.integration.service.iam.common.RoleTypeCatalog;
import com.digiwin.escloud.userv2.model.Customer;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.digiwin.escloud.aiouser.constant.AioUserConst.TMCD_ID_EXPIRED_FAILED_LIST;
import static com.digiwin.escloud.aiouser.model.tenant.ModuleContractStatus.*;

@Slf4j
@Service
@RefreshScope
public class TenantService implements ITenantService, ParamCheckHelp {

    //region
    @Value("${digiwin.user.defaultlanguage}")
    private String defaultLanguage;
    @Value("${digiwin.user.defaultserviceregion}")
    private String defaultServiceRegion;
    @Value("${digiwin.user.defaulttimezone}")
    private String defaultTimeZone;
    @Value("${digiwin.token.user.verifyuserid}")
    private String fromVerifyUserId;
    @Value("${digiwin.token.tenant.id}")
    private String fromTenantId;
    @Value("${digiwin.supplier.defaultsid:241199971893824}")
    Long defaultSid;
    @Value("${esc.integration.cacAddress}")
    private String cacAddress;
    @Value("${esc.integration.appToken}")
    private String appToken;
    @Value("${DAPAPPAddress}")
    private String DAPAPPAddress;
    @Value("${digi.market.url:https://market-test.digiwincloud.com.cn/sso-login}")
    private String digiMarketUrl;
    @Autowired
    private ITenantDao tenantDao;
    @Autowired
    private ISupplierDao supplierDao;
    @Autowired
    private ITpUserDao tpUserDao;
    @Autowired
    private IUserDao userDao;
    @Autowired
    private IProductDao productDao;
    @Autowired
    private ISupplierAiopsDao supplierAiopsDao;
    @Autowired
    private ITenantNotifyDao tenantNotifyDao;

    @Autowired
    private IamService iamService;
    @Autowired
    private IUserBaseService userBaseService;
    @Autowired
    private PermissionCopyService permissionCopyService;
    @Autowired
    private IUserService userService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private ISupplierAiopsService supplierAiopsService;
    @Autowired
    private TenantCache tenantCache;
    @Autowired
    private CacService cacService;
    @Autowired
    private PmcService pmcService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private AioItmsFeignClient aioItmsFeignClient;
    @Autowired
    private UserV2FeignClient userV2FeignClient;
    @Autowired
    private IamUtils iamUtils;
    @Autowired
    private ProductCache productCache;


    @Value("${asiaInfo.url}")
    private String asiaInfoUrl;

    @Value("${asiaInfo.accessId}")
    private String asiaInfoAccessId;

    @Value("${asiaInfo.appSecret}")
    private String asiaInfoAppSecret;

    @Value("${service.rights:AIEOM:environmentalOperationsServices,test1:dataProtectionServices,test:productionNonStopService}")
    private String serviceRightString;
    @Resource
    private ServiceMaintenanceService serviceMaintenanceService;

    // 限流大小为5
    private final Semaphore asiaInfoSemaphore = new Semaphore(5);
    private final Executor asiaInfoExecutor = Executors.newFixedThreadPool(5);


    private static final String ASIA_MODULE_EMAIL = "@digiwin.cloud.com";
    private static final String ASIA_ALIAS = "ASIA";
    private static final String ASIA_NAME = "鼎捷";
    private static final String ASIA_INFO = "ASIA_INFO";
    private static final String ASIA_EXE = "AisEsmNetService.exe, AisEsmUI.exe, hra.exe";
    //endregion

    @Override
    public List<Tenant> getTenants(String id, String name, long eid, Integer serviceType, Integer status, Date contractExpiryDate, Boolean attention, int page, int size) {
        List<String> authorizedProductCodes = getAuthorizedProductCodes(null, null, null);
        if (CollectionUtils.isEmpty(authorizedProductCodes)) {
            return null;
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        map.put("eid", eid);
        map.put("serviceType", serviceType);
        map.put("status", status);
        map.put("contractExpiryDate", contractExpiryDate);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("attention", attention);
        int start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        map.put("productCodes", authorizedProductCodes);

        map.put("userSid", RequestUtil.getHeaderUserSid());
        map.put("orgSid", RequestUtil.getHeaderOrgSid());
        String customerServiceRole = getCustomerServiceRole(RequestUtil.getHeaderToken(), RequestUtil.getHeaderUserSid());
        map.put("role", customerServiceRole);
        List<Long> tenantSids = tenantDao.getTenantSids(map);
        map.put("tenantSids", tenantSids);
        return tenantDao.getTenants(map);
    }

    @Override
    public List<TenantInfo> getTenantList() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        return tenantDao.getTenantList(map);
    }

    @Override
    public long getTenantsCount(String id, String name, long eid, Integer serviceType, Integer status, Date contractExpiryDate, Boolean attention) {
        List<String> authorizedProductCodes = getAuthorizedProductCodes(null, null, null);
        HashMap<String, Object> map = new HashMap<>();
        map.put("id", id);
        map.put("name", name);
        map.put("eid", eid);
        map.put("serviceType", serviceType);
        map.put("status", status);
        map.put("contractExpiryDate", contractExpiryDate);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("attention", attention);
        map.put("productCodes", authorizedProductCodes);

        map.put("userSid", RequestUtil.getHeaderUserSid());
        map.put("orgSid", RequestUtil.getHeaderOrgSid());
        String customerServiceRole = getCustomerServiceRole(RequestUtil.getHeaderToken(), RequestUtil.getHeaderUserSid());
        map.put("role", customerServiceRole);
        return tenantDao.getTenantsCount(map);
    }

    @Override
    public int getTenantsCount(Integer status, Integer moduleId, Date endDate, Boolean attention, String content) {
        return 0;
    }

    @Override
    public Tenant getTenantByServiceCode(String serviceCode) {
        return tenantDao.getTenantByServiceCode(serviceCode);
    }

    @Override
    public Tenant getTenantDetailByServiceCode(String serviceCode) {
        return tenantDao.getTenantDetailByServiceCode(serviceCode);
    }

    @Override
    public Tenant getTenantDetail(long tenantSid) {
        return tenantDao.getTenantDetail(tenantSid, RequestUtil.getHeaderSid());
    }

    @Override
    public TenantContract getTenantContract(long sid, long eid, String productCode, String status) {
        String contractStatus = null;
        if (org.apache.commons.lang3.StringUtils.isNotEmpty(status)) {
            // 根据状态码映射合同状态
            switch (status) {
                case "1":
                    contractStatus = "EXPIRED";
                    break;
                case "2":
                    contractStatus = "UNEXPIRED";
                    break;
                case "3":
                    contractStatus = "SOON_EXPIRE";
                    break;
            }
        }
        TenantContract tenantContract = tenantDao.getTenantContract(sid, eid, productCode, null, contractStatus);

        if (tenantContract == null) {
            // 修正, 查不到合約 NullPointerException 的問題
            return new TenantContract();
        }
        tenantContract.setMarketUrl(digiMarketUrl + "?userToken=" + RequestUtil.getHeaderToken());

        if (org.apache.commons.lang3.StringUtils.isNotBlank(serviceRightString)) {
            List<Map<String, Object>> serviceRightList = new ArrayList<>();
            for (String serviceRight : serviceRightString.split(",")) {
                Map<String, Object> serviceRightMap = new HashMap<>();
                String[] parts = serviceRight.split(":");
                if (parts.length == 2) {
                    String productId = parts[0];
                    String code = parts[1];
                    // serviceRightString  test1 和 test 是写死测试使用，实际的场景绝对不会出现productId=test1 和 productId=test
                    String marketUrl = "test1".equals(productId) || "test".equals(productId) ? null :
                            digiMarketUrl + "?userToken=" + RequestUtil.getHeaderToken() + "&routerLink=product-details/" + productId;

                    // 判断当前产品ID是否匹配，根据custLevel和code来判断isOpen
                    serviceRightMap.put("code", code);

                    boolean isOpen = false;
                    String custLevel = tenantContract.getCustLevel();

                    // 根据custLevel和code的组合来判断isOpen
                    if ("DA".equals(custLevel) && "environmentalOperationsServices".equals(code)) {
                        isOpen = true;
                    } else if ("DB".equals(custLevel) && "dataProtectionServices".equals(code)) {
                        isOpen = true;
                    } else if ("DC".equals(custLevel) && "productionNonStopService".equals(code)) {
                        isOpen = true;
                    } else {
                        // 其他情况保持原来的逻辑
                        isOpen = productId.equals(tenantContract.getProductId());
                    }

                    serviceRightMap.put("isOpen", isOpen);
                    serviceRightMap.put("marketUrl", marketUrl);
                    serviceRightList.add(serviceRightMap);
                }
            }
            // 只有147 才会返回服务权益，如果其他产品线 直接返回空
            tenantContract.setServiceRight("147".equals(tenantContract.getProductCode()) ? serviceRightList : new ArrayList<>());
        }
        return tenantContract;
    }


    @Override
    public TenantContractListResponse getTenantProductContractList(Long sid, Long eid) {
        TenantContractListResponse response = new TenantContractListResponse();
        //region 参数检查

        if (LongUtil.isEmpty(sid)) {
            ResponseCode code = ResponseCode.HEADER_IS_EMPTY;
            response.setCode(code.getCode());
            response.setErrMsg(code.getDynamicMsg("sid"));
            return response;
        }
        if (LongUtil.isEmpty(eid)) {
            ResponseCode code = ResponseCode.HEADER_IS_EMPTY;
            response.setCode(code.getCode());
            response.setErrMsg(code.getDynamicMsg("eid"));
            return response;
        }

        //endregion

        response.setTenantContractList(tenantDao.selectTenantProductContractList(sid, eid));
        ResponseCode code = ResponseCode.SUCCESS;
        response.setCode(code.getCode());
        return response;
    }

    @Override
    public List<Tenant> getTenantSimpleInfos(String name, String serviceCode, String taxCode, int page, int size) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("serviceCode", serviceCode);
        map.put("taxCode", taxCode);
        map.put("sid", RequestUtil.getHeaderSid());
        int start = 0;
        if (page > 0)
            start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return tenantDao.getTenantSimpleInfos(map);
    }

    @Override
    public long getTenantSimpleCount(String name, String serviceCode, String taxCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("name", name);
        map.put("serviceCode", serviceCode);
        map.put("taxCode", taxCode);
        map.put("sid", RequestUtil.getHeaderSid());
        return tenantDao.getTenantSimpleCount(map);
    }

    @Override
    public List<User> getTenantUsers(long tenantSid, String tenantId, String userName, int page, int size) {
        return this.getTenantUsers(tenantSid, tenantId, userName, page, size, new HashMap<>());
    }

    @Override
    public List<User> getTenantUsers(
            long tenantSid, String tenantId, String userName, int page, int size, Map<String, Object> otherLoginInfo
    ) {
//        String token = RequestUtil.getHeaderToken();
//        String appId = RequestUtil.getHeaderFuncAppId();

        // otherLoginInfo 主要是用來對付 http 上下文被中斷時, 還能拿到 login 的參數
        String token = ((Function<String, String>) innerToken -> {
            if (StringUtils.isEmpty(innerToken)) {
                return otherLoginInfo.getOrDefault("token", "").toString();
            }
            return innerToken;
        }).apply(RequestUtil.getHeaderToken());

        String appId = ((Function<String, String>) innerAppId -> {
            if (StringUtils.isEmpty(innerAppId)) {
                return otherLoginInfo.getOrDefault("appId", "").toString();
            }
            return innerAppId;
        }).apply(RequestUtil.getHeaderFuncAppId());

        HashMap<String, Object> map = new HashMap<>();
        map.put("tenantSid", tenantSid);
        map.put("userName", userName);
        int start = 0;
        if (page > 0)
            start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        List<User> tenantUsers = tenantDao.getTenantUsers(map);
        List<String> userIdList = tenantUsers.stream().map(o -> o.getId()).collect(Collectors.toList());
        List<UserNotifySettingResultRes> unsResList = tenantNotifyDao.getUserNotifySettingResultByUserModule(NotifyModuleEnum.WARNING.getCode(), userIdList);
        tenantUsers.stream().forEach(o -> {
            try {
                Map<String, List<Map<String, String>>> roleMap = iamService.getMultiUserRoleDataV2(token, tenantId,
                        Stream.of(o.getId()).collect(Collectors.toList()), RoleTypeCatalog.EsCloud);
                if (roleMap == null) {
                    return;
                }
                o.setRoles(roleMap.get(o.getId()));
                Map<String, Object> tenantUserGoodAuth = cacService.getTenantUserGoodAuth(token, tenantId, o.getId(), appId);
                o.setAppAuth(tenantUserGoodAuth);
                unsResList.stream()
                        .filter(uns -> o.getId().equals(uns.getUserId()))
                        .findFirst()
                        .ifPresent(uns -> {
                            o.setWarningNotifyEnable(uns.getEnabled());
                            o.setWarningNotifyLevels(uns.getReceiveLevels());
                        });

            } catch (Exception ex) {
                ex.printStackTrace();
                log.error("getUserRoles or getTenantUserGoodAuth error", ex);
            }
        });
        return tenantUsers;
    }

    @Override
    public long getTenantUsersCount(long tenantSid, String userName) {
        return tenantDao.getTenantUsersCount(tenantSid, userName);
    }

    @Override
    public int modifyTenantDescription(Long eid, String productCode, String description) {
        //region 参数检查

        if (LongUtil.isEmpty(eid)) {
            return 0;
        }

        if (StringUtils.isBlank(productCode)) {
            return 0;
        }

        //endregion

        return tenantDao.updateTenant(eid, productCode, description);
    }

    @Override
    public List<ContractState> getContractStateList() {
        return tenantDao.getContractStateList();
    }

    public void copyRolePermission(String toVerifyUserId, String toTenantId, AioPlatformEnum aioPlatformEnum) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    switch (aioPlatformEnum) {
                        case THIRDPARTY_CUSTOMER:
                        case SCB_DT:
                        case SCB_APP_A1:
                        case SCB_WEB_A1:
                            permissionCopyService.copyEsCloudRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, toVerifyUserId, toTenantId
                            );
                            break;
                        case THIRDPARTY_CUSTOMERSERVICE:
                        case SSM:
                        case AIOSSM:
                        case EASY_TALK:
                            permissionCopyService.copyOMRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, toVerifyUserId, toTenantId
                            );
                            break;
                        default:
                            permissionCopyService.copyEsCloudRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, toVerifyUserId, toTenantId
                            );
                            break;
                    }

                }
            });
        } catch (Exception ex) {
            log.error("copyRolePermission", ex);
        } finally {
            executorService.shutdown();
        }
    }

    public void copyRolePermission(String toIamToken, AioPlatformEnum aioPlatformEnum) {
        ExecutorService executorService = Executors.newSingleThreadExecutor();
        try {
            executorService.execute(new Runnable() {
                @Override
                public void run() {
                    switch (aioPlatformEnum) {
                        case THIRDPARTY_CUSTOMER:
                        case SCB_DT:
                        case SCB_APP_A1:
                        case SCB_WEB_A1:
                            permissionCopyService.copyEsCloudRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, "", "", toIamToken
                            );
                            break;
                        case THIRDPARTY_CUSTOMERSERVICE:
                        case SSM:
                        case AIOSSM:
                        case EASY_TALK:
                            permissionCopyService.copyOMRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, "", "", toIamToken
                            );
                            break;
                        default:
                            permissionCopyService.copyEsCloudRolePermissionToTargetTenant(
                                    fromVerifyUserId, fromTenantId, "", "", toIamToken
                            );
                            break;
                    }

                }
            });
        } catch (Exception ex) {
            log.error("copyRolePermission", ex);
        } finally {
            executorService.shutdown();
        }
    }

    private AuthoredUserGetResponse doRegisterTpTenant(TpAccount tpAccount) throws Exception {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        //租户信息
        String tpTenantId = tpAccount.getTpTenant().getTpTenantId();
        String tpTenantName = tpAccount.getTpTenant().getTpTenantName();
        String taxCode = tpAccount.getTpTenant().getTaxCode();
        TpAccountSetType tpAccountSetType = tpAccount.getTpTenant().getTpAccountSetType();
        String tpHisTenantId = tpAccount.getTpTenant().getTpHisTenantId();
        long sid = RequestUtil.getHeaderSid();
        //用户信息
        String tpUserId = tpAccount.getTpUser().getTpUserId();
        TpUserType tpUserType = tpAccount.getTpUser().getTpUserType();
        String tpUserName = tpAccount.getTpUser().getTpUserName();
        String tpUserEmail = tpAccount.getTpUser().getTpUserEmail();
        String tpUserTelephone = tpAccount.getTpUser().getTpUserTelephone();

        //先根据邮箱查用户，再根据手机查用户
        String userId = null;
        boolean userExist = false;
        if (!StringUtils.isEmpty(tpUserEmail)) {
            User userByEmail = userDao.getUserByEmail(tpUserEmail);
            if (userByEmail != null) {
                userId = userByEmail.getId();
                userExist = true;
            }
        }
        if (!userExist && !StringUtils.isEmpty(tpUserTelephone)) {
            User userByTelephone = userDao.getUserByTelephone(tpUserTelephone);
            if (userByTelephone != null) {
                userId = userByTelephone.getId();
                userExist = true;
            }
        }
        //找不到用户，就默认是个新用户，需要判断手机邮箱是否被注册过
        if (StringUtils.isEmpty(userId) && !StringUtils.isEmpty(tpUserEmail)) {
            CheckRes checkRes = userService.checkEmailExist(tpUserEmail);
            if (checkRes.getIsRegister()) {
                try {
                    UserInfo userInfo = userBaseService.getUserInfoByEmail(tpUserEmail);
                    userId = userInfo.getId();
                    userExist = true;
                } catch (Exception ex) {
                    log.info("doRegisterTpTenant userBaseService.doBaseLogin", ex);
                    res.setErrMsg(ResponseCode.MAIL_DUPLICATED.toString());
                    res.setCode(ResponseCode.MAIL_DUPLICATED.getMsg());
                    return res;
                }
            }
        }
        if (StringUtils.isEmpty(userId) && !StringUtils.isEmpty(tpUserTelephone)) {
            CheckRes checkRes = userService.checkTelephoneExist(tpUserTelephone);
            if (checkRes.getIsRegister()) {
                try {
                    UserInfo userInfo = userBaseService.getUserByTelephone(tpUserTelephone);
                    userId = userInfo.getId();
                    userExist = true;
                } catch (Exception ex) {
                    log.info("doRegisterTpTenant userBaseService.doBaseLogin", ex);
                    res.setErrMsg(ResponseCode.CELLPHONE_DUPLICATED.toString());
                    res.setCode(ResponseCode.CELLPHONE_DUPLICATED.getMsg());
                    return res;
                }
            }
        }
        userId = Optional.ofNullable(userId).orElse(StringUtils.isEmpty(tpUserEmail) ? tpUserTelephone : tpUserEmail);

        List<MappingInfo> mappingInfos = new ArrayList<>();
        MappingInfo mappingInfo = new MappingInfo();
        mappingInfo.setTenantId(tpTenantId);
        mappingInfo.setProviderId(AioUserConst.PROVIDERID);
        mappingInfo.setVerifyUserId(userId);
        mappingInfos.add(mappingInfo);

        List<RoleInfo> roleInfos = new ArrayList<>();
//        roleInfos.add(new RoleInfo("cosultant", "顧問"));
//        roleInfos.add(new RoleInfo("customerService", "客服人員"));
//        roleInfos.add(new RoleInfo("customerServiceAgent", "客服代理人"));
//        roleInfos.add(new RoleInfo("customerServiceSupervisor", "客服主管"));
        roleInfos.add(new RoleInfo("endERP", "終端ERP"));
        roleInfos.add(new RoleInfo("endUser", "一般用户"));
        roleInfos.add(new RoleInfo("mis", "管理者"));
        roleInfos.add(new RoleInfo("rdServcie", "研發服務"));
//        roleInfos.add(new RoleInfo("superManager", "服務雲_超級管理員"));
        roleInfos.add(new RoleInfo("superUser", "進階用戶"));

        roleInfos.add(new RoleInfo("ITMSUser", "運維一般用戶"));
//        roleInfos.add(new RoleInfo("ITMSService", "運維服務"));
//        roleInfos.add(new RoleInfo("ITMSPlugInManager", "運維插件管理"));
//        roleInfos.add(new RoleInfo("ITMSMaintain", "運維工程"));

        UserBasicInfo userBasicInfo = new UserBasicInfo();
        UserImportInfo user = new UserImportInfo();
        user.setId(userId);
        //iam必须要传name
        user.setName(tpUserName);
        //如果该手机或者邮箱未注册过iam，就新增一个新的用户，存在的话就不更新iam和
        if (!userExist) {
            user.setEmail(tpUserEmail);
            //存在邮箱用邮箱注册，否则用手机号注册
            user.setTelephone(StringUtils.isEmpty(tpUserEmail) ? tpUserTelephone : null);
        }
        user.setPassword(AioUserConst.DEFAULT_PWD);

        //企业用户为true，否则都为false
        user.setEnterprise(true);
        user.setTenantId(tpTenantId);
        user.setTenantName(tpTenantName);
        user.setTaxNo(taxCode);

        userBasicInfo.setUser(user);
        userBasicInfo.setMappingInfo(mappingInfos);
        userBasicInfo.setRoleInfo(roleInfos);
        List<UserBasicInfo> userBasicInfos = new ArrayList<>();
        userBasicInfos.add(userBasicInfo);

        log.info("registerTpTenant : " + SerializeUtil.JsonSerialize(userBasicInfos));
        String token = RequestUtil.getHeaderToken();
        iamService.importTenant(userBasicInfos);
        log.info("copyRolePermission : " + tpTenantId);
        copyRolePermission(userId, tpTenantId, AioPlatformEnum.THIRDPARTY_CUSTOMER);

//            IamAuthoredUser iamAuthoredUser = userBaseService.doBaseLogin(userBasicInfo.getUser().getId(), EncryptionUtil.getSHA256(userBasicInfo.getUser().getPassword()), IdentityType.token);
        IamAuthoredUser iamAuthoredUser = userBaseService.doBaseLoginWithTenant(userBasicInfo.getUser().getTenantId(), userBasicInfo.getUser().getId(), userBasicInfo.getUser().getPassword(), IdentityType.token);
        RoleType roleType = TpUserType.M.equals(tpUserType) ? RoleType.MIS : RoleType.User;
        permissionService.appendRoleToUser(iamAuthoredUser.getToken(), iamAuthoredUser.getUserId(), roleType);
        log.info("iam login : " + SerializeUtil.JsonSerialize(iamAuthoredUser));
        if (iamAuthoredUser != null) {
            UserPersonalInfo userPersonalInfo = new UserPersonalInfo(iamAuthoredUser.getSid(), defaultLanguage, defaultServiceRegion, defaultTimeZone);
            AuthoredUser authoredUser = userBaseService.tpUserloginAfterRegisterTpTenant(iamAuthoredUser, sid, AioUserConst.A1_SERVICE_PREFIX + tpTenantId, userPersonalInfo, tpAccount);
            log.info("tpUserloginAfterRegisterTpTenant : " + SerializeUtil.JsonSerialize(authoredUser));
            if (authoredUser != null) {
                stringRedisTemplate.opsForValue().set(authoredUser.getToken(), SerializeUtil.JsonSerialize(authoredUser), 1, TimeUnit.DAYS);
                tpAccount.getTenantContract().setSid(sid);
                tpAccount.getTenantContract().setEid(iamAuthoredUser.getTenantSid());
                SaveTenantContract(tpAccount.getTenantContract());
                res.setCode(ResponseCode.SUCCESS.toString());
                res.setAuthoredUser(authoredUser);
            } else
                res.setCode(ResponseCode.USER_REGISTER_ERROR.toString());
        } else
            res.setCode(ResponseCode.USER_REGISTER_ERROR.toString());
        return res;
    }

    @Override
    public AuthoredUserGetResponse registerTpTenant(TpAccount tpAccount) {
        AuthoredUserGetResponse res = new AuthoredUserGetResponse();
        int retryCount = 3, executeCount = 1;
        while (executeCount <= retryCount) {
            try {
                res = doRegisterTpTenant(tpAccount);
                break;
            } catch (Exception ex) {
                log.info("registerTpTenant execute count:{}", executeCount);
                log.error("registerTpTenant", ex);
                res.setErrMsg(ex.getMessage());
                res.setCode(ResponseCode.INTERNAL_ERROR.toString());
                executeCount++;
                try {
                    if (executeCount <= retryCount)
                        Thread.sleep(5000);
                } catch (Exception e) { //huly: 修复漏洞/bug InterruptedException 改成 Exception
                    log.error("registerTpTenant sleep", ex);
                }
            }
        }
        return res;
    }

    private int insertTenantContract(TenantContract tenantContract) throws Exception {
        return tenantDao.insertTenantContract(tenantContract);
    }

    private int updateTenantContract(long sid, long eid, String productCode, TenantContract tenantContract) throws Exception {
        return tenantDao.updateTenantContract(tenantContract);//sid, eid, productCode,
    }

    private int SaveTenantContract(TenantContract tenantContract) throws Exception {
        int res = 0;
        TenantContract updateTenantContract = tenantDao.getTenantContract(tenantContract.getSid(), tenantContract.getEid(), tenantContract.getProductCode(), null, null);
        if (updateTenantContract == null) {
            tenantContract.setId(SnowFlake.getInstance().newId());
            res = insertTenantContract(tenantContract);
        } else {
            if (!StringUtils.isEmpty(tenantContract.getContractState()))
                updateTenantContract.setContractState(tenantContract.getContractState());
            if (tenantContract.getContractStartDate() != null)
                updateTenantContract.setContractStartDate(tenantContract.getContractStartDate());
            if (tenantContract.getContractExpiryDate() != null)
                updateTenantContract.setContractExpiryDate(tenantContract.getContractExpiryDate());
            if (!StringUtils.isEmpty(tenantContract.getAccess()))
                updateTenantContract.setAccess(tenantContract.getAccess());
            if (tenantContract.getCanContact() != null)
                updateTenantContract.setCanContact(tenantContract.getCanContact());
            if (!StringUtils.isEmpty(tenantContract.getDescription()))
                updateTenantContract.setDescription(tenantContract.getDescription());
            if (!StringUtils.isEmpty(tenantContract.getAreaCode()))
                updateTenantContract.setAreaId(supplierDao.getSupplierAreaId(tenantContract.getSid(), tenantContract.getAreaCode()));
            if (!StringUtils.isEmpty(tenantContract.getIndustryCode()))
                updateTenantContract.setIndustryId(supplierDao.getSupplierIndustryId(tenantContract.getSid(), tenantContract.getIndustryCode()));
            res = updateTenantContract(tenantContract.getSid(), tenantContract.getEid(), tenantContract.getProductCode(), updateTenantContract);
        }

        return res;
    }

    /**
     * 更新租户合约信息
     *
     * @param tpAccount
     * @return
     */
    @Override
    public ResponseBase updateTpTenantContract(TpAccount tpAccount, String prefix) {
        ResponseBase res = new ResponseBase();
        try {
            //1. 查询租户信息
            Tenant tenant = tenantDao.getTenantByServiceCode(prefix + tpAccount.getTpTenant().getTpTenantId());
            if (tenant == null)
                res.setCode(ResponseCode.TENANT_EXISTS.getCode());
            else {
                //2. 更新租户合约信息
                long sid = RequestUtil.getHeaderSid();
                tpAccount.getTenantContract().setSid(sid);
                tpAccount.getTenantContract().setEid(tenant.getSid());
                if (SaveTenantContract(tpAccount.getTenantContract()) <= 0)
                    res.setCode(ResponseCode.UPDATE_FAILD.getCode());
            }
            res.setCode(ResponseCode.SUCCESS.getCode());
        } catch (Exception ex) {
            res.setCode(ResponseCode.INTERNAL_ERROR.getCode());
            res.setErrMsg(ex.getMessage());
        }
        return res;
    }

    @Override
    public void createTenantPermissions(String tenantId, String userId, String token, AioPlatformEnum aioPlatformEnum) {
        if (StringUtils.isEmpty(token)) {
            copyRolePermission(userId, tenantId, aioPlatformEnum);
        } else {
            copyRolePermission(token, aioPlatformEnum);
        }
    }

    @Override
    public PageInfo<TenantInfo> getSearchTenantList(String tenantSearchContent, boolean needAuth, String productCode, int page, int pageSize, long sid) {
        Long serviceIsvSid = RequestUtil.getHeaderServiceProviderSid();
        String type = RequestUtil.getHeaderPlatformType();
        Page pages = PageHelper.startPage(page, pageSize);
//        PageInfo<TenantInfo> pageInfo = new PageInfo<>(tenantDao.getSearchTenantList(tenantSearchContent));
        String customerServiceRole = null;
        if (needAuth) {
            customerServiceRole = getCustomerServiceRole(RequestUtil.getHeaderToken(), RequestUtil.getHeaderUserSid());
        }

        HashMap<String, Object> map = new HashMap<>();
        map.put("tenantSearchContent", tenantSearchContent);
        map.put("sid", sid);
        map.put("orgSid", RequestUtil.getHeaderOrgSid());
        map.put("userSid", RequestUtil.getHeaderUserSid());
        map.put("role", customerServiceRole);
        map.put("needAuth", needAuth);
        map.put("productCode", productCode);

        map.put("serviceIsvSid", serviceIsvSid);
        map.put("platformType", type);
        log.info("[getSearchTenantList] condition :{} ", map);
        tenantDao.getSearchTenantList(map);
        PageInfo<TenantInfo> pageInfo = new PageInfo<>(pages);
        return pageInfo;
    }

    @Override
    public List<TenantInfo> getSearchTenantListNoPage(String tenantSearchContent, boolean needAuth, long sid) {
        String customerServiceRole = null;
        if (needAuth) {
            customerServiceRole = getCustomerServiceRole(RequestUtil.getHeaderToken(), RequestUtil.getHeaderUserSid());
        }
        HashMap<String, Object> map = new HashMap<>();
        map.put("tenantSearchContent", tenantSearchContent);
        map.put("sid", sid);
        map.put("orgSid", RequestUtil.getHeaderOrgSid());
        map.put("userSid", RequestUtil.getHeaderUserSid());
        map.put("role", customerServiceRole);
        map.put("needAuth", needAuth);
        return tenantDao.getSearchTenantList(map);
    }

    @Override
    public Boolean checkTenantIdExist(Long tenantId) {
        return !LongUtil.isEmpty(tenantDao.selectTenantId(tenantId));
    }

    @Override
    public String checkTenantIdExistAndReturnName(Long tenantId) {
        List<Long> tenantIdList = new ArrayList<>(1);
        tenantIdList.add(tenantId);
        TenantNameListResponse response = getTenantNameList(tenantIdList);
        if (response == null) {
            return "";
        }
        if (!ResponseCode.SUCCESS.getCode().equals(response.getCode())) {
            return "";
        }
        List<TenantNameInfo> tenantNameInfoList = response.getTenantNameInfoList();
        if (CollectionUtils.isEmpty(tenantNameInfoList)) {
            return "";
        }
        String name = tenantNameInfoList.get(0).getName();
        //为了区别不存在与没有名称，因此当名称是空时，返回空白
        return StringUtils.isBlank(name) ? " " : name;
    }

    @Override
    public TenantNameListResponse getTenantNameList(List<Long> tenantIdList) {
        TenantNameListResponse response = new TenantNameListResponse();

        //region 参数检查

        if (checkParamIsEmpty(response, tenantIdList, "tenantIdList").isPresent()) {
            return response;
        }

        //endregion

        response.setTenantNameInfoList(tenantDao.selectTenantNameList(tenantIdList));
        response.setCode(ResponseCode.SUCCESS.getCode());
        return response;
    }

    @Override
    public TenantNameListResponse getTenantNameAndServiceCodeList(List<Long> tenantIdList) {
        TenantNameListResponse response = new TenantNameListResponse();


        response.setTenantMapDTO(tenantDao.selectTenantNameAndServiceCodeList(tenantIdList));
        response.setCode(ResponseCode.SUCCESS.getCode());
        return response;
    }

    public List<String> getAuthorizedProductCodes(Long userSid, Long eid, Long sid) {
        if (LongUtil.isEmpty(userSid)) {
            userSid = RequestUtil.getHeaderUserSid();
        }
        if (LongUtil.isEmpty(eid)) {
            eid = RequestUtil.getHeaderEid();
        }
        if (LongUtil.isEmpty(sid)) {
            sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
        }
        UserTenantMap userTenantMap = productDao.getUserTenantMap(userSid, eid, sid);
        if (userTenantMap == null) {
            return null;
        }
        String authorizedProductCode = userTenantMap.getAuthorizedProductCodes();
        if (StringUtils.isEmpty(authorizedProductCode)) {
            return null;
        }
        return Arrays.stream(authorizedProductCode.split(",")).collect(Collectors.toList());
    }

    private String getCustomerServiceRole(String token, long userSid) {
        List<Map<String, String>> userRoles = iamService.getUserRoles(token, null, userSid);
        boolean isDeptSupervisor = userRoles.stream().filter(role -> "customerServiceSupervisor".equals(role.get("roleId"))).findAny().isPresent();
        boolean isSuperManager = userRoles.stream().filter(role -> "superManager".equals(role.get("roleId"))).findAny().isPresent();
        String role;
        if (isSuperManager) {
            role = "superManager";
        } else {
            if (isDeptSupervisor) {
                role = "customerServiceSupervisor";
            } else {
                role = "customerService";
            }
        }
        return role;
    }

    @Override
    public ResponseBase getTenantExistedList(String content, Long userSid, int page, int size) {
        try {
            List<String> authorizedProductCodes = getAuthorizedProductCodes(userSid, null, null);
            if (CollectionUtils.isEmpty(authorizedProductCodes)) {
                return ResponseBase.error(ResponseCode.AUTH_PRODUCT_IS_NULL);
            }
            if (userSid == null || userSid == 0) {
                userSid = RequestUtil.getHeaderUserSid();
            }
            HashMap<String, Object> map = new HashMap<>();
            map.put("sid", RequestUtil.getHeaderSid());
            map.put("content", content);
            map.put("productCodes", authorizedProductCodes);
            map.put("userSid", userSid);
            int start = (page - 1) * size;
            map.put("start", start);
            map.put("size", size);
            map.put("role", getCustomerServiceRole(RequestUtil.getHeaderToken(), userSid));
            return ResponseBase.ok(new TenantInfoGetRes(tenantDao.getTenantExistedList(map), tenantDao.getTenantExistedCount(map)));
        } catch (Exception ex) {
            log.error("getTenantExistedList", ex);
            return ResponseBase.error(ex);
        }
    }

    @Override
    public Long getTenantSidByServiceCode(String serviceCode) {
        return tenantCache.getTenantSidByServiceCode(serviceCode);
    }

    @Override
    public List<SupplierTenantMap> getSupplierTenantMapListByServiceCodeList(List<String> serviceCodeList) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("serviceCodeList", serviceCodeList);
        return tenantDao.getSupplierTenantMapListByMap(map);
    }

    @Override
    public List<SupplierTenantMap> getSupplierTenantMapListByEidList(List<Long> eidList) {
        Map<String, Object> map = new HashMap<>(2);
        map.put("eidList", eidList);
        return tenantDao.getSupplierTenantMapListByMap(map);
    }

    @Override
    public List<Map<String, String>> getTenantNameByServiceCode(String serviceCode) {
        return tenantDao.getTenantNameByServiceCode("%" + serviceCode + "%");
    }

    @Override
    public int getTenantModuleCountByServiceCode(String serviceCode) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("serviceCode", serviceCode);
        return tenantDao.getTenantModuleCountByServiceCode(map);
    }

    //region 模组相关

    @Override
    public List<Tenant> getTenantModuleContracts(String status, String moduleId, Date endDate, Boolean attention, String content,
                                                 Long[] serviceIsvSids, int page, int size) {
        HashMap<String, Object> map = new HashMap<>();
        if (RequestUtil.getHeaderServiceProviderSid() > 0) {
            serviceIsvSids = ArrayUtils.add(serviceIsvSids, RequestUtil.getHeaderServiceProviderSid());
        }
        if (StringUtils.isNotBlank(status)) {
            map.put("statusList", Arrays.stream(status.split(",")).filter(StringUtils::isNotBlank)
                    .map(IntegerUtil::objectToInteger).filter(IntegerUtil::isNotEmpty)
                    .collect(Collectors.toList()));
        }
        if (StringUtils.isNotBlank(moduleId)) {
            map.put("moduleIdList", Arrays.stream(moduleId.split(",")).filter(StringUtils::isNotBlank)
                    .map(IntegerUtil::objectToInteger).filter(IntegerUtil::isNotEmpty)
                    .collect(Collectors.toList()));
        }
        map.put("endDate", endDate);
        map.put("attention", attention);
        map.put("content", content);
        map.put("serviceIsvSids", serviceIsvSids);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("userSid", RequestUtil.getHeaderUserSid());
        int start = (page - 1) * size;
        map.put("start", start);
        map.put("size", size);
        return tenantDao.getTenantModuleContracts(map);
    }

    @Override
    public List<TenantModuleContract> getTenantModuleContractsByServiceCode(
            String serviceCode, Long moduleId, Long... eid) {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", LongUtil.isEmpty(RequestUtil.getHeaderSid()) ? defaultSid : RequestUtil.getHeaderSid());
        map.put("serviceCode", serviceCode);
        map.put("moduleId", moduleId);
        if (ArrayUtils.isNotEmpty(eid)) {
            // 如果有傳 Eid 就用 eid 查
            map.put("eid", eid[0]);
            map.remove("serviceCode");
        }
        return tenantDao.getTenantModuleContractsByServiceCode(map);
    }

    @Override
    public int getTenantModuleContractsCount(String status, String moduleId, Date endDate, Boolean attention, String content, Long[] serviceIsvSids) {
        HashMap<String, Object> map = new HashMap<>();
        if (StringUtils.isNotBlank(status)) {
            map.put("statusList", Arrays.stream(status.split(",")).filter(StringUtils::isNotBlank)
                    .map(IntegerUtil::objectToInteger).filter(IntegerUtil::isNotEmpty)
                    .collect(Collectors.toList()));
        }
        if (RequestUtil.getHeaderServiceProviderSid() > 0) {
            serviceIsvSids = ArrayUtils.add(serviceIsvSids, RequestUtil.getHeaderServiceProviderSid());
        }
        if (StringUtils.isNotBlank(moduleId)) {
            map.put("moduleIdList", Arrays.stream(moduleId.split(",")).filter(StringUtils::isNotBlank)
                    .map(IntegerUtil::objectToInteger).filter(IntegerUtil::isNotEmpty)
                    .collect(Collectors.toList()));
        }
        map.put("endDate", endDate);
        map.put("attention", attention);
        map.put("content", content);
        map.put("serviceIsvSids", serviceIsvSids);
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("userSid", RequestUtil.getHeaderUserSid());
        return tenantDao.getTenantModuleContractsCount(map);
    }

    @Override
    public BaseResponse getTenantModuleContractEmptyList(Long eid) {
        //获取所有可选择的模组
        BaseResponse response = supplierAiopsService.getModuleAndClass(null);
        Optional<List<SupplierAiopsModule>> optSupplierAiopsModuleList = response.checkAndGetCurrentData();
        if (!optSupplierAiopsModuleList.isPresent()) {
            return response;
        }
        List<SupplierAiopsModule> supplierAiopsModuleList = optSupplierAiopsModuleList.get();
        if (CollectionUtils.isEmpty(supplierAiopsModuleList)) {
            return BaseResponse.ok(new ArrayList<>(0));
        }
        //创建新的租户模组合约，拷贝模组与类别数据，填充默认值
        List<TenantModuleContract> tenantModuleContractList = supplierAiopsModuleList.stream()
                .filter(x -> x != null)
                .map(x -> {
                    TenantModuleContract tmc = new TenantModuleContract();
                    BeanUtils.copyProperties(x, tmc);
                    tmc.setId(0L);
                    tmc.setModuleId(x.getId());
                    tmc.setEid(eid);
                    List<SupplierAiopsModuleClass> samcList = x.getSupplierAiopsModuleClassList();
                    if (!CollectionUtils.isEmpty(samcList)) {
                        tmc.setTenantModuleContractDetailList(samcList.stream().filter(y -> y != null).map(y -> {
                            TenantModuleContractDetail tmcd = new TenantModuleContractDetail();
                            BeanUtils.copyProperties(y, tmcd);
                            tmcd.setId(0L);
                            tmcd.setTmcId(0L);
                            tmcd.setSamcId(y.getId());
                            tmcd.setAvailableCount(0);
                            tmcd.setUsedCount(0);
                            return tmcd;
                        }).collect(Collectors.toList()));
                    }
                    List<SupplierAiopsModuleItem> supplierAiopsModuleItems = x.getSupplierAiopsModuleItems();
                    if (!CollectionUtils.isEmpty(supplierAiopsModuleItems)) {
                        tmc.setItemNo(supplierAiopsModuleItems.get(0).getItemNo());
                    }
                    return tmc;
                })
                .collect(Collectors.toList());
        return BaseResponse.ok(tenantModuleContractList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse saveTenantModuleContract(TenantModuleContract tenantModuleContract, String userName, Boolean asiaException) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(tenantModuleContract, "tenantModuleContract");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = tenantModuleContract.getEid();
        optResponse = checkParamIsEmpty(eid, "tenantModuleContract.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);
        tenantModuleContract.setSid(sid);
        //判断是否是亚信模组特殊处理
        boolean isAsiaInfoModule = getTenantModuleContract(sid)
                .stream()
                .filter(tmc -> "ASIA_VULN".equals(tmc.getModuleCode()))
                .anyMatch(tmc -> tmc.getId() == tenantModuleContract.getModuleId());

        if (isAsiaInfoModule) {
            asiaInfoTenantDeal(tenantModuleContract, asiaException);
        }


        tenantModuleContract.setOrderMethod(tenantModuleContract.getStatus() == ModuleContractStatus.TRIAL_EXPIRED.getIndex() ?
                ModuleOrderMethod.OPEN_TRIAL.getIndex() : ModuleOrderMethod.OPEN_SUBSCRIBE.getIndex());
        boolean hasNew = false;
        Long tmcId = tenantModuleContract.getId();
        if (LongUtil.isEmpty(tmcId)) {
            tmcId = SnowFlake.getInstance().newId();
            tenantModuleContract.setId(tmcId);
            hasNew = true;
            //新增场景，检查eid下是否已经存在同模组的合约
            BaseResponse response = checkExistTenantContractModule(sid, eid,
                    Stream.of(tenantModuleContract.getModuleId()).collect(Collectors.toList()));
            if (!response.checkIsSuccess()) {
                return response;
            }
            tenantModuleContract.setOrderMethod(tenantModuleContract.getStatus() == TRIALING.getIndex() ?
                    ModuleOrderMethod.OPEN_TRIAL.getIndex() : ModuleOrderMethod.OPEN_SUBSCRIBE.getIndex());
        } else {
            TenantModuleContract tenantContractById = tenantDao.getTenantContractById(tmcId);
            if ((tenantContractById.getStatus() == TRIALING.getIndex() ||
                    tenantContractById.getStatus() == ModuleContractStatus.TRIAL_EXPIRED.getIndex()) &&
                    tenantModuleContract.getStatus() == SUBSCRIBED.getIndex()) {
                tenantModuleContract.setOrderMethod(ModuleOrderMethod.TRIAL_OFFERED.getIndex());
            } else {
                tenantModuleContract.setOrderMethod(ModuleOrderMethod.CHANGE_AUTH.getIndex());
            }
        }
        //若模组合约的到期日小于等于今天，这里不做处理，交由定时任务去将其合约暂停
        TenantModuleContractHistory tenantModuleContractHistory = new TenantModuleContractHistory(tmcId, userName);
        BeanUtils.copyProperties(tenantModuleContract, tenantModuleContractHistory);
        tenantModuleContractHistory.setId(SnowFlake.getInstance().newId());
        List<TenantModuleContractHistory> tmchList = Stream.of(tenantModuleContractHistory).collect(Collectors.toList());
        List<TenantModuleContract> tmcList = Stream.of(tenantModuleContract).collect(Collectors.toList());
        setTenantModuleContractHistoriesOperateType(tmcList, tmchList);
        tenantDao.saveTenantModuleContractHistories(tmchList);
        tenantDao.saveTenantModuleContracts(tmcList);
        BaseResponse response = saveTenantModuleContractDetail(hasNew, true, tmcId,
                tenantModuleContract.getTenantModuleContractDetailList());
        if (!response.checkIsSuccess()) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return response;
        }
        //如果合约异动为试用到期或者订阅到期，就将其对应的运维实例授权暂停
        int status = tenantModuleContract.getStatus();
        if (status == ModuleContractStatus.TRIAL_EXPIRED.getIndex() ||
                status == ModuleContractStatus.SUBSCRIBED_EXPIRED.getIndex()) {
            stopAiopsInstanceAuthStatusByTmcIdList(Stream.of(tenantModuleContract.getId())
                    .collect(Collectors.toList()));
        }
        return BaseResponse.ok(tenantModuleContract.getId());
    }

    @Override
    public void asiaInfoTenantDeal(TenantModuleContract tenantModuleContract, Boolean asiaException) {
        // 检查本地租户是否存在
        TenantTp tenantTp = tenantDao.selectAsiaTenantByEid(tenantModuleContract.getSid(), tenantModuleContract.getEid());
        Tenant tenant = tenantDao.getTenant(tenantModuleContract.getEid());
        AsiaInfoTenant asiaInfoTenant = buildAsiaInfoTenant(tenantModuleContract, tenant, tenantTp);
        if (tenantTp != null) {
            //更新亚信模组信息
            String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/" + tenantTp.getTpTenantId();
            TenantTp existTenantTp = checkAsiaTenantExistByName(asiaInfoTenant.getAlias());
            if (Objects.isNull(existTenantTp)) {
                throw new RuntimeException("[asiaInfoTenantDeal] 亚信租户数据错误");
            }
            log.info("[asiaInfoTenantDeal] update asiaTenant:{}", JSONObject.toJSONString(asiaInfoTenant));
            tenantTp.setTpTenantId(existTenantTp.getTpTenantId());
            tenantTp.setTpTenantName(existTenantTp.getTpTenantName());
            tenantTp.setTpAlias(existTenantTp.getTpAlias());
            updateAsiaInfoTenant(apiUrl, asiaInfoTenant, tenantModuleContract, tenant, tenantTp, asiaException);
            return;
        }

        String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/create";
        // 创建新的租户
        createAsiaInfoTenant(apiUrl, asiaInfoTenant, tenantModuleContract, tenant);
        // 检查亚信租户是否存在
//        TenantTp existTenantTp = checkAsiaTenantExistByName(asiaInfoTenant.getAlias());
//        if (existTenantTp == null) {

//        } else {
        //应该不会出现 更新已存在的租户
//            updateTenantTp(tenantModuleContract, existTenantTp, tenant);
//        }
    }

    private void createAsiaInfoTenant(String apiUrl, AsiaInfoTenant asiaInfoTenant, TenantModuleContract tenantModuleContract, Tenant tenant) {
        String jsonString = JSONObject.toJSONString(asiaInfoTenant);
        HttpEntity<String> request = AsiaHeaderUtil.buildHeader(apiUrl, true, asiaInfoAccessId, asiaInfoAppSecret, jsonString, asiaInfoUrl);
        log.info("request:{}", request);
        try {
            ResponseEntity<String> exchange = restTemplate.exchange(apiUrl, HttpMethod.POST, request, String.class);
            log.info("[createAsiaInfoTenant] exchange:{}", exchange);
            AsiaInfoResponse asiaInfoResponse = JSONObject.parseObject(exchange.getBody(), AsiaInfoResponse.class);
            if (AsiaInfoResponse.dataExists(asiaInfoResponse)) {
                tenantDao.insertTenantTp(buildTenantTp(tenantModuleContract, asiaInfoResponse.getData(), tenant, null));
            }
        } catch (Exception re) {
            log.info("[createAsiaInfoTenant] Error:{}", re.getMessage(), re);
            if (re.getMessage().contains("\"code\":200")) {
                AsiaInfoResponse asiaInfoResponse = JSONObject.parseObject(re.getMessage(), AsiaInfoResponse.class);
                if (AsiaInfoResponse.dataExists(asiaInfoResponse)) {
                    tenantDao.insertTenantTp(buildTenantTp(tenantModuleContract, asiaInfoResponse.getData(), tenant, null));
                }
            } else {
                throw new RuntimeException("[asiaInfoTenantDeal] 创建租户异常: " + re.getMessage(), re);
            }

        }
    }

    private void updateAsiaInfoTenant(String apiUrl, AsiaInfoTenant asiaInfoTenant
            , TenantModuleContract tenantModuleContract, Tenant tenant, TenantTp existTenantTp, Boolean asiaException
    ) {
        try {
            String jsonString = JSONObject.toJSONString(asiaInfoTenant);
            HttpEntity<String> request = AsiaHeaderUtil.buildHeader(apiUrl, true, asiaInfoAccessId, asiaInfoAppSecret, jsonString, asiaInfoUrl);
            ResponseEntity<AsiaInfoResponse> asiaInfoResponse = restTemplate.exchange(apiUrl, HttpMethod.PUT, request, AsiaInfoResponse.class);
            if (!AsiaInfoResponse.isSuccess(asiaInfoResponse)) {
                throw new RuntimeException("[updateAsiaInfoTenant] 更新租户失败: " + asiaInfoResponse.getBody().getMessage());
            }
            tenantDao.updateTenantTp(buildTenantTp(tenantModuleContract, null, tenant, existTenantTp));
        } catch (Exception e) {
            if (asiaException) {
                throw new RuntimeException("[asiaInfoTenantDeal] 更新租户异常: " + e.getMessage(), e);
            }
            log.error("[updateAsiaInfoTenant]  创建租户异常: {}", e.getMessage(), e);
        }
    }

    private void updateTenantTp(TenantModuleContract tenantModuleContract, TenantTp existTenantTp, Tenant tenant) {
        tenantDao.insertTenantTp(buildTenantTp(tenantModuleContract, existTenantTp, tenant));
    }

    private AsiaInfoTenant buildAsiaInfoTenant(TenantModuleContract tenantModuleContract, Tenant tenant, TenantTp tenantTp) {
        AsiaInfoTenant ait = new AsiaInfoTenant();
        if (Objects.nonNull(tenantTp)) {
            ait.setTenant_name(StringUtils.isEmpty(tenantTp.getTpTenantName()) ? ASIA_NAME + tenant.getSid() : tenantTp.getTpTenantName());
            ait.setEmail(StringUtils.isEmpty(tenantTp.getTpEmail()) ? tenant.getSid() + ASIA_MODULE_EMAIL : tenantTp.getTpEmail());
            ait.setAlias(StringUtils.isEmpty(tenantTp.getTpAlias()) ? ASIA_ALIAS + "_" + tenant.getSid() : tenantTp.getTpAlias());
        } else {
            // 下面是设置默认值的示例
            ait.setTenant_name(ASIA_NAME + tenant.getSid());
            ait.setEmail(tenant.getSid() + ASIA_MODULE_EMAIL);
            ait.setAlias(ASIA_ALIAS + "_" + tenant.getSid());
        }

        List<TenantModuleContractDetail> tmcdList = tenantModuleContract.getTenantModuleContractDetailList();
        List<Long> samcIds = tmcdList.stream().map(TenantModuleContractDetail::getSamcId).collect(Collectors.toList());

        Map<String, Object> conditionMap = new HashMap<>();
        conditionMap.put("samcIdList", samcIds);
        List<SupplierAiopsModuleClass> samcList = supplierAiopsDao.selectSamcByMap(conditionMap);

        Map<Long, SupplierAiopsModuleClass> samcMap = samcList.stream()
                .collect(Collectors.toMap(SupplierAiopsModuleClass::getId, Function.identity()));
        AsiaInfoTenant.License license = new AsiaInfoTenant.License();
        tmcdList.forEach(x -> {
            SupplierAiopsModuleClass samc = samcMap.get(x.getSamcId());
            if (Objects.nonNull(samc)) {
                switch (samc.getClassCode()) {
                    case "ASIA_VULN_DESKTOP":
                        license.setDesktop(createLicenseDetails(tenantModuleContract, x));
                        break;
                    case "ASIA_VULN_SERVER":
                        license.setServer(createLicenseDetails(tenantModuleContract, x));
                        break;
                }
            }
        });
        ait.setLicense(license);
        log.info("[buildAsiaInfoTenant] AsiaInfoTenant:{}", JSONObject.toJSONString(ait));
        return ait;
    }

    private AsiaInfoTenant.LicenseDetails createLicenseDetails(TenantModuleContract tenantModuleContract, TenantModuleContractDetail tmcd) {
        AsiaInfoTenant.LicenseDetails licenseDetails = new AsiaInfoTenant.LicenseDetails();

        licenseDetails.setVulnerability(ModuleContractStatus.contractAvailable(tenantModuleContract.getStatus()));
        licenseDetails.setStart_time(DateUtil.localDateTime2Timestamp(DateUtil.parseToLocalDateTime(tenantModuleContract.getStartDate())));
        licenseDetails.setExpiration_time(DateUtil.localDateTime2MaxLocalDateTime(DateUtil.parseToLocalDateTime(tenantModuleContract.getEndDate())));
        licenseDetails.setTotal(tmcd.getAvailableCount());

        return licenseDetails;
    }

    private TenantTp buildTenantTp(TenantModuleContract tenantModuleContract, Map<String, Object> rspData, Tenant tenant, TenantTp existTenantTp) {
        TenantTp tt = new TenantTp();
        if (Objects.nonNull(existTenantTp)) {
            BeanUtils.copyProperties(existTenantTp, tt);
        } else {
            tt.setId(SnowFlake.getInstance().newId());
            if (!CollectionUtils.isEmpty(rspData)) {
                tt.setTpTenantId(rspData.getOrDefault("tenant_uuid", "").toString());
                tt.setTpAgentDownloadUrl(rspData.getOrDefault("windows_download_url", "").toString());
            }
            tt.setTpTenantName(ASIA_NAME + tenant.getSid());
            tt.setTpEmail(tenant.getSid() + ASIA_MODULE_EMAIL);
            tt.setTpAlias(ASIA_ALIAS + "_" + tenant.getSid());
            tt.setEid(tenantModuleContract.getEid());
            tt.setSid(tenantModuleContract.getSid());
            tt.setTpCode(ASIA_INFO);
            tt.setTpProgram(ASIA_EXE);
        }

        return tt;
    }

    private TenantTp buildTenantTp(TenantModuleContract tenantModuleContract, TenantTp ttp, Tenant tenant) {
        TenantTp tt = new TenantTp();
        tt.setEid(tenantModuleContract.getEid());
        tt.setSid(tenantModuleContract.getSid());
        tt.setId(SnowFlake.getInstance().newId());
        tt.setTpTenantId(ttp.getTpTenantId());
        tt.setTpTenantName(ttp.getTpTenantName());
        tt.setTpEmail(tenant.getEmail());
        tt.setTpCode(ttp.getTpCode());
        tt.setTpAlias(ttp.getTpAlias());
        tt.setTpProgram(ASIA_EXE);
        return tt;
    }

    private TenantTp checkAsiaTenantExist(String url, HttpMethod method, HttpEntity request) {
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<AsiaInfoResponse> asiaInfoResponse = restTemplate.exchange(url, method, request, AsiaInfoResponse.class);
        if (AsiaInfoResponse.isSuccess(asiaInfoResponse) && AsiaInfoResponse.dataExists(asiaInfoResponse.getBody())) {
            TenantTp tp = new TenantTp();
            tp.setTpTenantId(asiaInfoResponse.getBody().getData().get("tenant_uuid").toString());
            tp.setTpTenantName(asiaInfoResponse.getBody().getData().get("tenant_name").toString());
            tp.setTpAlias(asiaInfoResponse.getBody().getData().get("alias").toString());
            tp.setTpCode(ASIA_INFO);
            tp.setLicense(asiaInfoResponse.getBody().getData().get("license"));
            return tp;
        }
        return null;
    }

    private TenantTp checkAsiaTenantExistById(String tenantId) {
        String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/" + tenantId;
        HttpEntity request = AsiaHeaderUtil.buildHeader(apiUrl, false, asiaInfoAccessId, asiaInfoAppSecret, null, asiaInfoUrl);
        return checkAsiaTenantExist(apiUrl, HttpMethod.GET, request);
    }

    private TenantTp checkAsiaTenantExistByName(String aliasTenantName) {
        String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/alias/" + aliasTenantName;
        HttpEntity request = AsiaHeaderUtil.buildHeader(apiUrl, false, asiaInfoAccessId, asiaInfoAppSecret, null, asiaInfoUrl);
        return checkAsiaTenantExist(apiUrl, HttpMethod.GET, request);
    }


    @Override
    public BaseResponse saveTenantModuleContractDetail(boolean hasNew, boolean isComplete, Long tmcId,
                                                       List<TenantModuleContractDetail> tmcdList) {
        //huly: 修复漏洞/bug boolean tmcdListIsEmpty = false; 改成 boolean tmcdListIsEmpty = CollectionUtils.isEmpty(tmcdList);
        boolean tmcdListIsEmpty = CollectionUtils.isEmpty(tmcdList);
        if (hasNew && tmcdListIsEmpty) {
            //是新的且没有内容，离开
            return BaseResponse.ok(0);
        }
        //如果不是新的，且没有内容，就全部清除并离开
        if (tmcdListIsEmpty) {
            //是完整数据才做删除
            if (isComplete) {
                return BaseResponse.ok(tenantDao.deleteTenantModuleContractDetailByTmcId(tmcId));
            }
            return BaseResponse.ok(0);
        }
        //查询已有数据
        List<TenantModuleContractDetail> oriTmcdList = tenantDao.selectTenantModuleContractDetailOnly(tmcId);
        if (CollectionUtils.isEmpty(oriTmcdList)) {
            //原始没有数据，直接新增并离开
            return saveTenantModuleContractDetailCore(tmcId, tmcdList, null);
        }
        //产生原始数据字典(后面排除已经存在的，进行删除)
        Map<String, TenantModuleContractDetail> oriTmcdMap = oriTmcdList.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(ModuleContractDetailBase::getBusinessKey, (x) -> x, (x, y) -> y));
        //处理新增或更新，并产生字典
        BaseResponse<Integer> response = saveTenantModuleContractDetailCore(tmcId, tmcdList, oriTmcdMap);
        if (!response.checkIsSuccess()) {
            return response;
        }
        int result = response.getData();
        //处理删除的内容
        if (CollectionUtils.isEmpty(oriTmcdMap) || !isComplete) {
            return BaseResponse.ok(result);
        }
        //是完整数据才做删除
        List<Long> needDeleteIdList = oriTmcdMap.values().stream()
                .map(ModuleContractDetailBase::getId).collect(Collectors.toList());
        return BaseResponse.ok(result + tenantDao.deleteTenantModuleContractDetailByTmcdIdList(needDeleteIdList));
    }

    private BaseResponse saveTenantModuleContractDetailCore(Long tmcId, List<TenantModuleContractDetail> tmcdList,
                                                            Map<String, TenantModuleContractDetail> map) {
        if (CollectionUtils.isEmpty(tmcdList)) {
            return BaseResponse.ok(0);
        }
        Map<String, TenantModuleContractDetail> tmcdMap;
        if (map == null) {
            tmcdMap = new HashMap<>(0);
        } else {
            tmcdMap = map;
        }
        //整理(填充tmcId等字段)並校验设置的可使用数量，与已使用数量是否有冲突(可使用数量必须>=已使用数量)
        List<TenantModuleContractDetail> invalidTmcdList = tmcdList.stream().filter(Objects::nonNull).filter(x -> {
            if (LongUtil.isEmpty(x.getId())) {
                x.setId(SnowFlake.getInstance().newId());
            }
            x.setTmcId(tmcId);
            //已使用量不可以异动，自动异动为0(仅新增时有效)
            if (BooleanUtil.objectToBoolean(x.getIsUpdateUsedCount()) && Objects.nonNull(x.getUsedCount())) {
                x.setUsedCount(x.getUsedCount());
            } else {
                x.setUsedCount(0);
            }

            String key = x.getBusinessKey();
            TenantModuleContractDetail oriTmcd = tmcdMap.remove(key);
            Integer availableCount = x.getAvailableCount();
            if (IntegerUtil.isEmpty(availableCount)) {
                //没有可用数量(一般是新增场景)，填0，并返回false
                x.setAvailableCount(0);
                return false;
            }
            if (oriTmcd == null) {
                //旧的没找到，直接返回false
                return false;
            }
            //如果找到填充tmcdId，后面会使用到
            x.setId(oriTmcd.getId());
            Integer oriUsedCount = oriTmcd.getUsedCount();
            if (!BooleanUtil.objectToBoolean(x.getIsUpdateUsedCount()) && oriTmcd.getIsContainHoldAuth() != null && !oriTmcd.getIsContainHoldAuth()) {
                //旧的无须授权，不做管控，将使用数量填回
                x.setUsedCount(oriUsedCount);
                return false;
            }
            if (availableCount >= oriUsedCount) {
                //若可用数量>=已用数量也返回false
                return false;
            }
            //其他的就是不合法的，填充已用数量，作为异常讯息使用
            x.setUsedCount(oriUsedCount);
            return true;
        }).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(invalidTmcdList)) {
            return BaseResponse.error(ResponseCode.TENANT_MODULE_CONTRACT_DETAIL_AVAILABLE_COUNT_INVALID,
                    invalidTmcdList);
        }
        // 根据是否需要更新使用数量将列表分组
        Map<Boolean, List<TenantModuleContractDetail>> groupedTmcdList = tmcdList.stream()
                .collect(Collectors.groupingBy(tmcd -> BooleanUtil.objectToBoolean(tmcd.getIsUpdateUsedCount())));

        // 分别处理需要和不需要更新使用数量的列表
        int totalCount = 0;
        List<TenantModuleContractDetail> tmcdListV2 = groupedTmcdList.getOrDefault(true, Collections.emptyList());
        if (!CollectionUtils.isEmpty(tmcdListV2)) {
            totalCount += tenantDao.saveTenantModuleContractDetailListV2(tmcdListV2);
        }

        List<TenantModuleContractDetail> tmcdListV1 = groupedTmcdList.getOrDefault(false, Collections.emptyList());
        if (!CollectionUtils.isEmpty(tmcdListV1)) {
            totalCount += tenantDao.saveTenantModuleContractDetailList(tmcdListV1);
        }

        return BaseResponse.ok(totalCount);
    }

    @Override
    public BaseResponse fixInvalidTmcdId(TenantModuleContract tmc) {
        if (Objects.isNull(tmc)) {
            return BaseResponse.ok(0);
        }
        List<TenantModuleContractDetail> tmcdList = tmc.getTenantModuleContractDetailList();
        if (CollectionUtils.isEmpty(tmcdList)) {
            return BaseResponse.ok(0);
        }
        Long eid = tmc.getEid();
        if (LongUtil.isEmpty(eid)) {
            return BaseResponse.ok(0);
        }
        return fixInvalidTmcdIdByTmcdList(eid, tmcdList);
    }

    private BaseResponse fixInvalidTmcdIdByTmcdList(Long eid, Collection<TenantModuleContractDetail> tmcdList) {
        //tmcd表中有针对tmcId+samcId做唯一索引，约束了tmcId因此同个tmc底下tmcdId与samcId是一对一的关系
        Map<Long, Long> samcIdGroupTmcdIdMap = tmcdList.stream()
                .collect(Collectors.toMap(TenantModuleContractDetail::getSamcId, TenantModuleContractDetail::getId,
                        (x, y) -> y));
        Map<String, Object> conditionMap = new HashMap<>(2);
        conditionMap.put("sid", RequestUtil.getHeaderSidOrDefault(defaultSid));
        conditionMap.put("samcIdList", samcIdGroupTmcdIdMap.keySet());
        List<SupplierAiopsModuleClass> samcList = supplierAiopsDao.selectSamcByMap(conditionMap);
        if (CollectionUtils.isEmpty(samcList)) {
            return BaseResponse.ok(0);
        }
        List<Map<String, Object>> mapList = samcList.stream().map(x -> {
            Long samcId = x.getId();
            List<SupplierAiopsModuleClassDetail> samcdList = x.getSamcdList();
            if (CollectionUtils.isEmpty(samcdList)) {
                return null;
            }
            Long tmcdId = samcIdGroupTmcdIdMap.get(samcId);
            if (LongUtil.isEmpty(tmcdId)) {
                return null;
            }
            List<Long> samcdIdList = new ArrayList<>(samcdList.size());
            boolean isContainHoldAuth = samcdList.stream().peek(y -> samcdIdList.add(y.getId()))
                    .anyMatch(y -> BooleanUtils.toBoolean(y.getHoldAuth()));
            Map<String, Object> map = new HashMap<>();
            map.put("eid", eid);
            map.put("tmcdId", tmcdId);
            map.put("isContainHoldAuth", isContainHoldAuth);
            map.put("onlyTmcdIdEmpty", true);
            map.put("samcdIdList", samcdIdList);
            return map;
        }).filter(x -> !CollectionUtils.isEmpty(x)).collect(Collectors.toList());
        BaseResponse<Map<String, Map<String, Integer>>> response =
                aioItmsFeignClient.fixAiopsInstanceTmcdIdByMapList(mapList);
        if (!response.checkIsSuccess()) {
            return response;
        }
        Map<String, Map<String, Integer>> resultMap = response.getData();
        if (CollectionUtil.isNotEmpty(resultMap)) {
            //修正数量
            resultMap.forEach((k, v) -> {
                Map<Long, Integer> tmcdIdUsedCountMap = v.entrySet().stream().collect(Collectors.toMap(
                        x -> LongUtil.objectToLong(x.getKey()), x -> IntegerUtil.objectToInteger(x.getValue())));
                recalculateTmcdUsedCountCore(tmcdIdUsedCountMap.keySet(), tmcdIdUsedCountMap,
                        !BooleanUtils.toBoolean(k));
            });
        }
        return BaseResponse.ok();
    }

    @Override
    public List<TenantModuleContractHistory> getTenantModuleContractHistories(long moduleContractId) {
        return tenantDao.getTenantModuleContractHistories(moduleContractId);
    }

    @Override
    public void setTenantModuleContractHistoriesOperateType(Collection<TenantModuleContract> tmcColl,
                                                            List<TenantModuleContractHistory> tmchList) {
        // 确保tmchList与tmcList数量一致且一一对应
        if (tmchList.size() != tmcColl.size()) {
            log.error("[setTenantModuleContractHistoriesOperateType] tmchList != tmcList, tmchList size: {}, tmcList size: {}", tmchList.size(), tmcColl.size());
            return;
        }
        List<TenantModuleContract> tmcList = new ArrayList<>(tmcColl);
        for (int i = 0; i < tmcList.size(); i++) {
            TenantModuleContract tmc = tmcList.get(i);
            TenantModuleContractHistory tmch = tmchList.get(i);

            List<TenantModuleContractHistory> dbTmchList = tenantDao.getTenantModuleContractHistories(tmc.getId());

            boolean isFirstSubscribe = CollectionUtils.isEmpty(dbTmchList);
            if (isFirstSubscribe) {
                tmch.setOperateType(OperateType.FIRST_SUBSCRIBE);
                continue;
            }

            switch (tmc.getStatus()) {
                // 试用
                case 1:
                    if (dbTmchList.stream().noneMatch(x -> x.getOperateType() == OperateType.TRIAL)) {
                        tmch.setOperateType(OperateType.TRIAL);
                    }
                    break;
                // 订阅
                case 3:
                    if (dbTmchList.stream().noneMatch(x -> x.getOperateType() == OperateType.FORMAL_SUBSCRIBE)) {
                        tmch.setOperateType(OperateType.FORMAL_SUBSCRIBE);
                    }
                    break;
                default:
                    tmch.setOperateType(OperateType.CHANGE_AUTHORIZATION);
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public ResponseBase fixTmchData() {
        List<TenantModuleContractHistory> dbTmchList = tenantDao.selectAllTenantModuleContractHistories();
        Map<Long, List<TenantModuleContractHistory>> tmcIdMap =
                dbTmchList.stream().collect(Collectors.groupingBy(TenantModuleContractHistory::getModuleContractId));

        for (Map.Entry<Long, List<TenantModuleContractHistory>> tmchEntry : tmcIdMap.entrySet()) {
            List<TenantModuleContractHistory> tmchList = tmchEntry.getValue();
            tmchList.sort(Comparator.comparing(TenantModuleContractHistory::getCreateTime));

            tmchList.stream()
                    .filter(x -> Objects.isNull(x.getOperateType()))
                    .findFirst().ifPresent(x -> {
                        x.setOperateType(OperateType.FIRST_SUBSCRIBE);
                    });
            tmchList.stream().filter(x -> x.getStatus() == 1)
                    .filter(x -> Objects.isNull(x.getOperateType()))
                    .findFirst().ifPresent(x -> {
                        x.setOperateType(OperateType.TRIAL);
                    });
            tmchList.stream().filter(x -> x.getStatus() == 3)
                    .filter(x -> Objects.isNull(x.getOperateType()))
                    .findFirst().ifPresent(x -> {
                        x.setOperateType(OperateType.FORMAL_SUBSCRIBE);
                    });
            tmchList.forEach(x -> {
                if (Objects.isNull(x.getOperateType())) {
                    x.setOperateType(OperateType.CHANGE_AUTHORIZATION);
                }
                tenantDao.updateTenantModuleContractHistories(x.getId(), x.getOperateType());
            });
            log.info("[fixTmchData] {} ", tmchEntry);


        }
        return ResponseBase.ok();
    }

    @Override
    public BaseResponse checkExistTenantContractModule(Long sid, Long eid, List<Long> samIdList) {
        //检查eid下是否已经存在同模组的合约
        //若存在不允许添加(与慧娟确认过，同运维商下同租户同模组不允许重复纪录)
        List<TenantModuleContract> existValidModuleContractModuleCodeList =
                getExistTenantContractModule(sid, eid, samIdList);
        if (!CollectionUtils.isEmpty(existValidModuleContractModuleCodeList)) {
            String itemString = existValidModuleContractModuleCodeList.stream().map(x -> x.getModuleCode())
                    .collect(Collectors.joining(","));
            return BaseResponse.dynamicError(existValidModuleContractModuleCodeList,
                    ResponseCode.TENANT_MODULE_CONTRACT_ALREADY_EXIST, eid, itemString);
        }
        return BaseResponse.ok();
    }

    @Override
    public List<TenantModuleContract> getExistTenantContractModule(Long sid, Long eid, Collection<Long> samIdList) {
        return tenantDao.selectExistTenantModuleContract(sid, eid, samIdList);
    }

    private List<SupplierAiopsModule> getTenantModuleContract(Long sid) {
        return tenantDao.selectSupplierAiopsModule(sid);
    }

    @Override
    public BaseResponse getTenantAllModuleContractDetail(Long eid, List<String> moduleCodeList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSid();
        return BaseResponse.ok(tenantDao.selectTenantAllModuleContractDetail(sid, eid, moduleCodeList));
    }

    @Override
    public BaseResponse getTenantModuleContractClassDetailByAiopsItem(Long eid, List<String> aiopsItemList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSid();
        return BaseResponse.ok(tenantDao.selectTenantModuleContractClassDetailByAiopsItem(sid, eid, aiopsItemList));
    }


    @Override
    public BaseResponse getTenantModuleContractClassDetailByAiopsItemListV2(Long eid, List<String> aiopsItemList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSid();
        String marketUrl = getDynamicDbQueryMarketUrl("ifnull(sam.cloudMarketProductId, '')");
        return BaseResponse.ok(
                tenantDao.selectTenantModuleContractClassDetailByAiopsItemV2(sid, eid, marketUrl, aiopsItemList)
        );
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public BaseResponse modifyTenantModuleContractDetailUsedCountByTmcdList(Long eid,
                                                                            List<TenantModuleContractDetail> tmcdList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(tmcdList, "tmcdList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        List<TenantModuleContractDetail> noneAuthEmptyTmcdIdList = new ArrayList<>();
        List<TenantModuleContractDetail> failedList = tmcdList.stream()
                .filter(Objects::nonNull)
                .filter(x -> {
                    if (!BooleanUtils.toBoolean(x.getIsContainHoldAuth()) && LongUtil.isEmpty(x.getId())) {
                        //挑出无须授权，且tmcdId为空的纪录，后面进行数据修正
                        noneAuthEmptyTmcdIdList.add(x);
                        return false;
                    }
                    return true;
                })
                .filter(x -> tenantDao.updateTenantModuleContractDetailUsedCountByTmcdId(x.getId(),
                        x.getUsedCount(), x.getIsContainHoldAuth()) <= 0)
                .collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(failedList)) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return BaseResponse.dynamicError(failedList, ResponseCode.AIOPS_AUTH_OVERFLOW, eid);
        }
        //处理无须授权且空的tmcdId(因旧数据影响，可能tmc存在但tmcd不存在，这里会修正不存在的tmcd，但仅限无须授权的运维项目)
        if (CollectionUtil.isNotEmpty(noneAuthEmptyTmcdIdList)) {
            //只能处理不存在tmcd，无法处理不存在tmc(因为tmc存在合约起迄服务人员等信息，这里无法取得)
            Map<Long, TenantModuleContractDetail> samcIdTmcdMap = new HashMap<>();
            noneAuthEmptyTmcdIdList.stream()
                    .map(x -> Pair.of(x, x.getSupplierAiopsModuleClassDetailList()))
                    .filter(x -> CollectionUtil.isNotEmpty(x.getRight()))
                    .forEach(x -> x.getRight().forEach(y -> samcIdTmcdMap.put(y.getSamcId(), x.getLeft())));
            List<TenantModuleContract> tmcList = tenantDao.selectTmcListByEidSamcIdList(eid, samcIdTmcdMap.keySet());
            if (CollectionUtils.isEmpty(tmcList)) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return BaseResponse.dynamicError(noneAuthEmptyTmcdIdList, ResponseCode.AIOPS_AUTH_OVERFLOW, eid);
            }
            //挑出无效的tmc纪录
            List<TenantModuleContractDetail> invalidTmcdList = new ArrayList<>();
            //因为tmc列表是透过samcId列表串查出来的，其中tmcd里面的samcId是来源于samc表，因此tmcd列表不可能为空
            Map<Long, List<TenantModuleContractDetail>> needInsertTmcIdTmcdListMap = tmcList.stream()
                    .map(x -> Pair.of(x, x.getTenantModuleContractDetailList()))
                    .filter(x -> CollectionUtil.isNotEmpty(x.getRight()))
                    .filter(x -> {
                        if (LongUtil.isEmpty(x.getLeft().getId())) {
                            invalidTmcdList.addAll(x.getRight());
                            return false;
                        }
                        return true;
                    }).map(Pair::getRight)
                    .flatMap(Collection::stream)
                    .peek(x -> x.setId(SnowFlake.getInstance().newId()))
                    .collect(Collectors.groupingBy(ModuleContractDetailBase::getTmcId));
            if (CollectionUtil.isNotEmpty(invalidTmcdList)) {
                String itemString = invalidTmcdList.stream()
                        .map(TenantModuleContractDetail::getSupplierAiopsModuleClassDetailList)
                        .flatMap(Collection::stream).map(SupplierAiopsModuleClassDetail::getAiopsItem)
                        .collect(Collectors.joining(","));
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return BaseResponse.dynamicError(invalidTmcdList, ResponseCode.TENANT_MODULE_CONTRACT_NOT_EXIST,
                        eid, itemString);
            }
            if (CollectionUtil.isNotEmpty(needInsertTmcIdTmcdListMap)) {
                //保存tmcd
                optResponse = needInsertTmcIdTmcdListMap.entrySet().stream()
                        .map(x -> saveTenantModuleContractDetail(true, false,
                                x.getKey(), x.getValue()))
                        .filter(x -> !x.checkIsSuccess())
                        .findFirst();
                if (optResponse.isPresent()) {
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return optResponse.get();
                }
                //TODO: 无须授权，先不做修正使用数量，因链路关系，可能造成死锁
//                    //最后修正下使用数量
//                    return optResponse.orElseGet(() ->
//                            fixInvalidTmcdIdByTmcdList(eid, needInsertTmcIdTmcdListMap.values().stream()
//                                    .flatMap(Collection::stream).collect(Collectors.toList())));
            }
        }
        return BaseResponse.ok();
    }

    @Override
    public BaseResponse getTenantValidSamcdIdList(Long eid, List<String> aiopsItemList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        return BaseResponse.ok(tenantDao.selectTenantValidSamcdIdList(sid, eid, aiopsItemList));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse recalculateTmcdUsedCount(Long eid, Boolean isLimited, List<String> aiopsItemList) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(eid, "eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        if (isLimited == null) {
            isLimited = true;
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        List<Map<String, Object>> tmcdMapList = tenantDao.selectTmcdMapListByEid(sid, eid, aiopsItemList);
        if (CollectionUtils.isEmpty(tmcdMapList)) {
            return BaseResponse.ok(0);
        }
        Map<Boolean, List<Long>> containHoldAuthGroupTmcdIdMap = tmcdMapList.stream().filter(Objects::nonNull)
                .collect(Collectors.groupingBy(x -> BooleanUtil.objectToBoolean(x.get("isContainHoldAuth")),
                        Collectors.mapping(x -> LongUtil.objectToLong(x.get("tmcdId")), Collectors.toList())));

        Boolean finalIsLimited = isLimited;
        return containHoldAuthGroupTmcdIdMap.entrySet().stream().map(x -> {
            Boolean isContainHoldAuth = x.getKey();
            List<Long> tmcdIdList = x.getValue();
            Set<Long> tmcdIdSet = tmcdIdList.stream().filter(y -> !LongUtil.isEmpty(y)).collect(Collectors.toSet());
            BaseResponse<Map<Long, Integer>> response = aioItmsFeignClient.getAuthedCountMapByTmcdIdList(eid,
                    isContainHoldAuth, tmcdIdSet);
            if (!response.checkIsSuccess()) {
                return response;
            }
            Map<Long, Integer> tmcdIdUsedCountMap = response.getData();
            if (tmcdIdUsedCountMap == null) {
                tmcdIdUsedCountMap = new HashMap<>(0);
            }
            return recalculateTmcdUsedCountCore(tmcdIdSet, tmcdIdUsedCountMap,
                    isContainHoldAuth && finalIsLimited);
        }).filter(x -> !x.checkIsSuccess()).findFirst().orElseGet(() -> BaseResponse.ok(tmcdMapList.size()));
    }

    private BaseResponse recalculateTmcdUsedCountCore(Collection<Long> tmcdIdCollection,
                                                      Map<Long, Integer> tmcdIdUsedCountMap, boolean isLimited) {

        List<Long> notUpdateTmcdId = tmcdIdCollection.stream().map(x -> {
            int authedCount = IntegerUtil.objectToInteger(tmcdIdUsedCountMap.get(x));
            if (IntegerUtil.isEmpty(authedCount)) {
                //尝试转成字串在取(因为json传递的关系，key会变成字串)
                authedCount = IntegerUtil.objectToInteger(tmcdIdUsedCountMap.get(LongUtil.safeToString(x)));
                if (IntegerUtil.isEmpty(authedCount)) {
                    authedCount = 0;
                }
            }
            int effectRow = tenantDao.updateTenantModuleContractDetailUsedCountByTmcdId(x, authedCount, isLimited);
            if (isLimited && effectRow == 0) {
                return x;
            }
            return null;
        }).filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(notUpdateTmcdId)) {
            return BaseResponse.ok(tmcdIdCollection.size());
        }
        return BaseResponse.error(ResponseCode.RECALCULATE_TMCD_USED_COUNT_PART_FAILED, notUpdateTmcdId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BaseResponse recalculateTmcdUsedCountByAuthUsedRequest(AuthUsedRequest authUsedRequest) {
        //region 参数检查

        Optional<BaseResponse> optResponse = checkParamIsEmpty(authUsedRequest, "authUsedRequest");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Long eid = authUsedRequest.getEid();
        optResponse = checkParamIsEmpty(eid, "authUsedRequest.eid");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        List<String> aiopsItemList = authUsedRequest.getAiopsItemList();
        optResponse = checkParamIsEmpty(aiopsItemList, "authUsedRequest.aiopsItemList");
        if (optResponse.isPresent()) {
            return optResponse.get();
        }

        Map<Long, Integer> tmcdIdUsedCountMap = authUsedRequest.getTmcdIdUsedCountMap() == null ? new HashMap<>() :
                authUsedRequest.getTmcdIdUsedCountMap();

        Boolean isLimited = authUsedRequest.getIsLimit();
        if (isLimited == null) {
            isLimited = true;
        }

        //endregion

        Long sid = RequestUtil.getHeaderSidOrDefault(defaultSid);

        //如果字典数量与运维项目数量不同，依据运维项目重新串查出缺漏的，并填充已用数量为0
        if (tmcdIdUsedCountMap.size() != aiopsItemList.size()) {
            List<Map<String, Object>> tmcdMapList = tenantDao.selectTmcdMapListByEid(sid, eid, aiopsItemList);
            if (!CollectionUtils.isEmpty(tmcdMapList)) {
                tmcdMapList.stream().filter(Objects::nonNull).map(x -> LongUtil.objectToLong(x.get("tmcdId")))
                        .filter(x -> !LongUtil.isEmpty(x)).filter(x -> !tmcdIdUsedCountMap.containsKey(x))
                        .forEach(x -> tmcdIdUsedCountMap.put(x, 0));
            }
        }

        return recalculateTmcdUsedCountCore(tmcdIdUsedCountMap.keySet(),
                tmcdIdUsedCountMap, isLimited);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void stopAiopsInstanceAuthStatusByTmcIdList(List<Long> tmcIdList) {
        //尝试从redis取出上次失败的
        List<Long> tmcdIdList;
        Long size = LongUtil.objectToLong(redisTemplate.opsForSet().size(TMCD_ID_EXPIRED_FAILED_LIST));
        boolean hasRedisTmcdIdList = size > 0;
        if (hasRedisTmcdIdList) {
            List<Long> redisTmcdIdList = redisTemplate.opsForSet().pop(TMCD_ID_EXPIRED_FAILED_LIST, size);
            //重新串查一次租户模组合约，避免因为延迟，造成合约已经重新生效，但是将其改为失效问题
            tmcdIdList = tenantDao.selectExactExpireTmcdIdList(redisTmcdIdList);
        } else {
            tmcdIdList = new ArrayList<>();
        }
        tmcdIdList.addAll(tenantDao.selectTmcdIdListByTmcIdList(tmcIdList));
        if (CollectionUtils.isEmpty(tmcdIdList)) {
            log.info("got empty tmcdIdList: " + SerializeUtil.JsonSerialize(tmcIdList));
            return;
        }
        //去重复
        tmcdIdList = tmcdIdList.stream().distinct().collect(Collectors.toList());
        String exStr;
        try {
            List<Map<String, Object>> partTmcdList = tenantDao.selectTmcdHoldAuthRelateByTmcdIdList(tmcdIdList);
            // 24/03/07 调整实例未授权逻辑，异动更新时将recoverableAuth更新为1
            BaseResponse<Map<Long, Integer>> response =
                    aioItmsFeignClient.modifyAiopsInstanceAuthStatusByContractExpire(partTmcdList);
            if (response.checkIsSuccess()) {
                //刷新已授权数量
                Map<Long, Integer> map = response.getData();
                if (map == null) {
                    map = new HashMap<>(0);
                }
                recalculateTmcdUsedCountCore(tmcdIdList, map, false);
                return;
            }
            exStr = response.getErrMsg();
        } catch (Exception ex) {
            ex.printStackTrace();
            exStr = ex.toString();
        }
        //可能不通，尝试将结果存在redis，并在下一次进行恢复
        redisTemplate.opsForSet().add(TMCD_ID_EXPIRED_FAILED_LIST, tmcdIdList.toArray(new Long[0]));
        log.error(String.format("tmcIdList expire failed: %s\r\nexception:%s",
                SerializeUtil.JsonSerialize(tmcdIdList), exStr));
    }

    @Override
    public BaseResponse checkTenantAppAuth(String serviceCode, String appId) {
        // 檢查該租戶是否有AIEOM的授權
        if (ObjectUtils.isEmpty(getTenantAppIdInfo(serviceCode, appId))) {
            // 该租户尚未授权应用「企业运维服务云」
            return BaseResponse.error(ResponseCode.TENANT_NO_APP_AUTH);
        } else {
            return BaseResponse.ok();
        }
    }

    public String getTenantAppIdInfo(String tenantId, String appId) {
        // tenantId 租戶ID
        // appId 應用ID
        // https://cac-test.digiwincloud.com.cn/api/cac/v4/authorizations/tenants/99990000/goods/AIEOM
        try {
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            headers.set("digi-middleware-auth-user", RequestUtil.getHeaderToken());
//            headers.set("digi-middleware-auth-app", appToken);
            HashMap hashMap = restTemplate.getForObject(cacAddress + "/api/cac/v4/authorizations/tenants/" + tenantId + "/goods/" + appId, HashMap.class);

            if (!ObjectUtils.isEmpty(hashMap)) {
                log.info("getTenantAppIdInfo " + "serviceCode:" + tenantId + " AppId:" + hashMap.get("code").toString());
                return hashMap.get("code").toString();
            } else {
                return null;
            }
        } catch (Exception ex) {
            log.error("getTenantAppIdInfo", ex);
            return null;
        }
    }

    //endregion

    @Override
    public List<Map<String, String>> getTenantApps(String esUserId, String tenantId, String productCode, String iamToken) {
        List<Map<String, String>> list = new ArrayList<>();
        try {
            if (StringUtils.isEmpty(iamToken)) {
                IamAuthoredUser iamUserInfo = iamUtils.getIamUserInfo(esUserId, tenantId);
                if (ObjectUtils.isEmpty(iamUserInfo)) {
                    return list;
                }
                iamToken = iamUserInfo.getToken();
            }

            HttpHeaders requestHeaders = new HttpHeaders();
            requestHeaders.setContentType(MediaType.APPLICATION_JSON);
            requestHeaders.set("digi-middleware-auth-user", iamToken);
//            requestHeaders.set("digi-middleware-auth-app", appToken);
            HttpEntity httpEntity = new HttpEntity<>(requestHeaders);
            ResponseEntity<String> response = restTemplate.exchange(cacAddress + "/api/cac/v4/authorizations/tenants/" + tenantId, HttpMethod.GET, httpEntity, String.class);
            if (!HttpStatus.OK.equals(response.getStatusCode())) {
                log.error("获取iam的app失败");
                return list;
            }
            String body = response.getBody();
            if (org.springframework.util.StringUtils.isEmpty(body) || "[]".equals(body)) {
                return list;
            }
            Gson gson = new Gson();
            JsonArray jsonArray = gson.fromJson(body, JsonArray.class);
            if (jsonArray != null && jsonArray.size() > 0) {
                jsonArray.forEach(k -> {
                    JsonObject jsonObject = k.getAsJsonObject();
                    Map<String, String> map = new HashMap<>();
                    map.put("code", jsonObject.get("code").getAsString());
                    map.put("displayName", jsonObject.get("displayName").getAsString());
                    map.put("authOpen", jsonObject.get("authOpen").getAsString());
                    map.put("expired", jsonObject.get("expired").getAsString());
                    map.put("itemId", jsonObject.get("itemId").getAsString());
                    map.put("categoryId", jsonObject.get("categoryId").getAsString());
                    list.add(map);
                });

                if (!CollectionUtils.isEmpty(list)) {
                    ResponseBase responseBase = userV2FeignClient.getERPSystemCodeList(productCode);
                    if (ResponseCode.SUCCESS.getCode().equals(responseBase.getCode())) {
                        Object data = responseBase.getData();
                        if (!ObjectUtils.isEmpty(data)) {
                            List<String> mysqlList = (List<String>) data;
                            return list.stream().filter(k -> mysqlList.contains(k.get("code"))).collect(Collectors.toList());
                        }
                    }
                }
                return list;
            }
        } catch (Exception e) {
            log.error("获取客户家的应用失败：{}", e.toString());
        }
        return list;
    }

    private List<Map<String, String>> getTenantERPSystemCodes(String iamToken, String appToken, String tenantId) {
        List<Map<String, String>> list = new ArrayList<>();
        List<Map<String, Object>> result = cacService.getTenantERPSystemCodes(iamToken, appToken, tenantId);
        if (!CollectionUtils.isEmpty(result)) {
            result.forEach(k -> {
                Map<String, String> map = new HashMap<>();
                map.put("code", k.get("code") != null ? k.get("code").toString() : "");
                map.put("displayName", k.get("displayName") != null ? k.get("displayName").toString() : "");
                map.put("authOpen", k.get("authOpen") != null ? k.get("authOpen").toString() : "");
                map.put("expired", k.get("expired") != null ? k.get("expired").toString() : "");
                map.put("itemId", k.get("itemId") != null ? k.get("itemId").toString() : "");
                map.put("categoryId", k.get("categoryId") != null ? k.get("categoryId").toString() : "");
                list.add(map);
            });
        }
        return list;
    }


    private List<Map<String, String>> getTenantGoods(String iamToken, String tenantId) {
        List<Map<String, String>> list = new ArrayList<>();
        long eid = 0L;
        ResponseBase res = userV2FeignClient.getTenantBasicDetail(tenantId);
        if (ResponseCode.SUCCESS.toString().equals(res.getCode()) && !ObjectUtils.isEmpty(res.getData())) {
            Customer customer = JSON.parseObject(JSON.toJSONString(res.getData()), Customer.class);
            eid = customer.getEid();
        }
        if (eid == 0L) return list;
        List<Map<String, Object>> result = pmcService.getTenantGoods(iamToken, eid);
        if (!CollectionUtils.isEmpty(result)) {
            result.forEach(k -> {
                Map<String, String> map = new HashMap<>();
                map.put("code", k.get("code") != null ? k.get("code").toString() : "");
                map.put("displayName", k.get("name") != null ? k.get("name").toString() : "");
                map.put("authOpen", k.get("authOpen") != null ? k.get("authOpen").toString() : "");
                map.put("expired", k.get("expired") != null ? k.get("expired").toString() : "");
                map.put("itemId", k.get("itemId") != null ? k.get("itemId").toString() : "");
                map.put("categoryId", k.get("categoryId") != null ? k.get("categoryId").toString() : "");
                list.add(map);
            });
        }
        return list;
    }

    private List<Map<String, String>> dealApps(List<Map<String, String>> list, String productCode, String serviceRegion) {
        List<Map<String, String>> resultList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            ResponseBase responseBase = userV2FeignClient.getERPSystemCodeListV2(productCode, serviceRegion);
            if (ResponseCode.SUCCESS.getCode().equals(responseBase.getCode())) {
                Object data = responseBase.getData();
                if (!ObjectUtils.isEmpty(data)) {
                    List<Map<String, String>> mysqlList = (List<Map<String, String>>) data;

                    for (int k = 0; k < list.size(); k++) {
                        Map<String, String> map = list.get(k);
                        if (codeInMysqlList(map.get("code"), mysqlList)) {
                            Map<String, String> mapTemp = new HashMap<>();
                            mapTemp.put("code", baseCodeInMysqlList(map.get("code"), mysqlList));
                            mapTemp.put("ErpSystemCode", mapTemp.get("code"));
                            mapTemp.put("displayName", map.get("displayName"));
                            mapTemp.put("ErpSystemName", mapTemp.get("displayName"));
                            mapTemp.put("authOpen", map.get("authOpen"));
                            mapTemp.put("expired", map.get("expired"));
                            mapTemp.put("itemId", map.get("itemId"));
                            mapTemp.put("categoryId", map.get("categoryId"));
                            mapTemp.put("level", levelInMysqlList(map.get("code"), mysqlList));
                            resultList.add(mapTemp);
                        }
                    }
                }
            }
        }
        return resultList;
    }

    private List<Map<String, String>> DAPApps(String iamToken, String appToken, String tenantId, String productCode, String serviceRegion) {
        List<Map<String, String>> resultList = new ArrayList<>();
        //1 先从iam 云端获取模组,将云端模组和资管模组合并取交集
        resultList = dealApps(getTenantERPSystemCodes(iamToken, appToken, tenantId), productCode, serviceRegion);

        //3 如何云端没有，获取iam地端模组
        if (CollectionUtils.isEmpty(resultList)) {
            resultList = dealApps(getTenantGoods(iamToken, tenantId), productCode, serviceRegion);
        }

        return resultList;

    }

    @Override
    public List<Map<String, String>> getV2TenantApps(String esUserId, String tenantId, String productCode, String iamToken, String serviceRegion) {

        List<Map<String, String>> list = new ArrayList<>();

        try {
            int source = productCache.getERPSystemCodeSource(productCode);//默认 0 表示模组从资管取，如果是1 表示从DAP取
            if (source == 1) {
                if (StringUtils.isEmpty(iamToken)) {
                    IamAuthoredUser iamUserInfo = iamUtils.getIamUserInfo(esUserId, tenantId);
                    if (ObjectUtils.isEmpty(iamUserInfo)) {
                        return list;
                    }
                    iamToken = iamUserInfo.getToken();
                }
                return DAPApps(iamToken, appToken, tenantId, productCode, serviceRegion);
            } else {
                ResponseBase responseBase = userV2FeignClient.getBaseERPSystemCodeList(productCode, serviceRegion);
                if (ResponseCode.SUCCESS.getCode().equals(responseBase.getCode())) {
                    Object data = responseBase.getData();
                    if (!ObjectUtils.isEmpty(data)) {
                        Object data1 = responseBase.getData();
                        if (!ObjectUtils.isEmpty(data1)) {
                            return (List<Map<String, String>>) data1;
                        }
                    }
                }
            }


        } catch (Exception e) {
            log.error("获取客户家的应用失败：{}", e.toString());
        }
        return list;
    }

    public String baseCodeInMysqlList(String code, List<Map<String, String>> list) {
        if (StringUtils.isEmpty(code) || CollectionUtils.isEmpty(list)) return "";
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            if (!StringUtils.isEmpty(map.get("DAPSystemCode")) && map.get("DAPSystemCode").equals(code)) {
                return map.get("ErpSystemCode");
            }
        }
        return "";
    }

    public String levelInMysqlList(String code, List<Map<String, String>> list) {
        if (StringUtils.isEmpty(code) || CollectionUtils.isEmpty(list)) return "";
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            if (!StringUtils.isEmpty(map.get("DAPSystemCode")) && map.get("DAPSystemCode").equals(code)) {
                return map.get("level");
            }
        }
        return "";
    }

    public boolean codeInMysqlList(String code, List<Map<String, String>> list) {
        if (StringUtils.isEmpty(code) || CollectionUtils.isEmpty(list)) return false;
        for (int i = 0; i < list.size(); i++) {
            Map<String, String> map = list.get(i);
            if (map.get("DAPSystemCode").equals(code)) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<TenantModuleContract> getTenantHoldAuthModules() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", RequestUtil.getHeaderEid());
        map.put("marketUrl", getDynamicDbQueryMarketUrl("ifnull(sam.cloudMarketProductId, '')"));
        return tenantDao.getTenantHoldAuthModules(map);
    }

    @Override
    public List<TenantModuleContract> getTenantHoldAuthExpirationStatus(long eid) {
        HashMap<String, Object> map = new HashMap<>();
//        map.put("eid", RequestUtil.getHeaderEid());
        if (eid > 0L) {
            map.put("eid", eid);
        }
        map.put("marketUrl", getDynamicDbQueryMarketUrl("ifnull(sam.cloudMarketProductId, '')"));
        return tenantDao.getTenantHoldAuthExpirationStatus(map);
    }


    private String getDynamicDbQueryMarketUrl(String dbFieldName) {
        //        String marketUrl = digiMarketUrl + "?userToken=" + RequestUtil.getHeaderToken() + "&routerLink=product-details/" + productId;
        StringBuilder sb = new StringBuilder();
        sb.append(digiMarketUrl);
        sb.append("?userToken=");
        sb.append(RequestUtil.getHeaderToken());
        sb.append("&routerLink=product-details/");
        return "CONCAT('" + sb.toString() + "', " + dbFieldName + ")";
    }

    @Override
    public List<TenantModuleContract> getModulesByCondition(ModuleQryReq moduleQryReq) {
        moduleQryReq.setEid(RequestUtil.getHeaderEid());
        moduleQryReq.setSid(RequestUtil.getHeaderSid());
        return tenantDao.getModulesByCondition(moduleQryReq);
    }

    /**
     * 20250106 討論後新增
     * * 根據目前會話識別碼檢索租用戶合約模組清單。
     *
     * @return 包含租戶合約模組資訊的 TenantModuleContract 物件列表
     */
    @Override
    public List<TenantModuleContract> getTenantContractModules() {
        HashMap<String, Object> map = new HashMap<>();
        map.put("sid", RequestUtil.getHeaderSid());
        map.put("eid", RequestUtil.getHeaderEid());
        return tenantDao.getTenantContractModules(map);
    }

    @Override
    public ResponseBase<List<String>> getAuthTmc(Long eid) {
        List<Integer> statusList
                = Lists.newArrayList(TRIALING.getIndex(), SUBSCRIBED.getIndex(), ANNUAL_MAINTENANCE_SERVICES.getIndex());
        List<String> strings = tenantDao.selectAuthTmc(eid, statusList);
        return ResponseBase.ok(strings);
    }

    @Override
    public List<TenantModuleContract> getServiceTenantHoldAuthModules() {

        HashMap<String, Object> map = new HashMap<>();
        map.put("eid", RequestUtil.getHeaderEid());
        return tenantDao.getServiceTenantHoldAuthModules(map);
    }

    @Override
    public ResponseBase getTenantServiceCodeIsExisted(String serviceCode) {
        Long eid = tenantCache.getTenantSidByServiceCode(serviceCode);
        if (eid != null) {
            return ResponseBase.ok(eid);
        }
        try {
            ResponseBase res = userV2FeignClient.getTenantBasicDetail(serviceCode);
            if (ObjectUtils.isEmpty(res)) {
                return ResponseBase.error(ResponseCode.INTERNAL_ERROR);
            }
            if (ResponseCode.SUCCESS.toString().equals(res.getCode()) && !ObjectUtils.isEmpty(res.getData())) {
                Customer customer = JSON.parseObject(JSON.toJSONString(res.getData()), Customer.class);
                return ResponseBase.error(ResponseCode.SERVICE_CODE_IN_ESCLOUD, customer.getEid());
            } else if (ResponseCode.SUCCESS.toString().equals(res.getCode()) && ObjectUtils.isEmpty(res.getData())) {
                return ResponseBase.error(ResponseCode.SERVICE_CODE_NOT_EXIST);
            } else {
                return res;
            }
        } catch (Exception e) {
            log.error("userV2FeignClient.getTenantBasicDetail", e);
            return ResponseBase.error(e);
        }
    }

    @Override
    public ResponseBase saveCrmContract() {
        return null;
    }

    @Override
    public ResponseBase<List<TenantModuleContractDetail>> getTmcdListByTmcIdList(List<Long> tmcIdList) {
        List<TenantModuleContractDetail> longs = tenantDao.selectTmcdListByTmcIdList(tmcIdList);
        return ResponseBase.okT(longs);
    }

    @Override
    public ResponseBase fixTmcdUseCount(List<Map<String, Object>> tmcdUseCountList) {
        log.info("fixTmcdUseCount:{}", JSONObject.toJSONString(tmcdUseCountList));

        tmcdUseCountList.stream().filter(i -> Objects.nonNull(i.get("useCount"))).forEach((i) ->
                tenantDao.updateTenantModuleContractDetailUsedCountByTmcdId(Long.parseLong(i.get("tmcdId").toString()),
                        Integer.parseInt(i.get("useCount").toString()), null));

        tmcdUseCountList.stream().filter(i -> Objects.isNull(i.get("useCount")))
                .forEach((i) ->
                        tenantDao.subTenantModuleContractDetailUsedCountByTmcdId(Long.parseLong(i.get("tmcdId").toString())));

        return ResponseBase.ok();
    }

    /**
     * 修复亚信历史数据，可能只会使用一次的功能
     *
     * @return
     */
    @Override
    public ResponseBase fixAsiaTmc(long staffId, String userName, List<Long> eidList) {

        TenantTpParams ttp = new TenantTpParams();
        Long sid = 241199971893824L;
        Long moduleId = 40001L;
        ttp.setTpCode("ASIA_INFO");
        ttp.setSid(sid);
        List<TenantTp> tenantTpList = tenantDao.selectAllAsiaTenant(ttp);
        if (!CollectionUtils.isEmpty(eidList)) {
            tenantTpList = tenantTpList.stream().filter(tp -> eidList.contains(tp.getEid())).collect(Collectors.toList());
        }
        tenantTpList.
                forEach(tp -> {
                    TenantTp tenantTp = checkAsiaTenantExistByName(tp.getTpAlias());
                    List<TenantModuleContract> tmcList = tenantDao.selectExistTenantModuleContract(sid, tp.getEid(), Stream.of(moduleId).collect(Collectors.toList()));
                    if (Objects.nonNull(tenantTp)) {
                        Map<String, Object> license = (Map<String, Object>) tenantTp.getLicense();
                        Map<String, Object> desktop = (Map<String, Object>) license.get("desktop");
                        Map<String, Object> server = (Map<String, Object>) license.get("server");
                        if (!CollectionUtils.isEmpty(desktop) || !CollectionUtils.isEmpty(server)) {
                            List<TenantModuleContractDetail> tmcdList = new ArrayList<>();
                            long desktopStartTime = desktop != null && desktop.containsKey("start_time") ? LongUtil.objectToLong(desktop.get("start_time")) * 1000L : 0;
                            long serverStartTime = server != null && server.containsKey("start_time") ? LongUtil.objectToLong(server.get("start_time")) * 1000L : 0;
                            long desktopExpirationTime = desktop != null && desktop.containsKey("expiration_time") ? LongUtil.objectToLong(desktop.get("expiration_time")) * 1000L : 0;
                            long serverExpirationTime = server != null && server.containsKey("expiration_time") ? LongUtil.objectToLong(server.get("expiration_time")) * 1000L : 0;

                            long maxStart = Math.max(desktopStartTime, serverStartTime);
                            long maxExpira = Math.max(desktopExpirationTime, serverExpirationTime);

                            Date startTime = new Date(maxStart);
                            Date expirationTime = new Date(maxExpira);
                            TenantModuleContract tmc;
                            if (CollectionUtils.isEmpty(tmcList)) {
                                tmc = new TenantModuleContract();
                                tmc.setSid(sid);
                                tmc.setEid(tp.getEid());
                                tmc.setModuleId(moduleId);
                                tmc.setStatus(1);
                                tmc.setStartDate(startTime);
                                tmc.setEndDate(expirationTime);
                            } else {
                                tmc = tmcList.get(0);
                            }
                            tmc.setServiceIsvSid(sid);
                            tmc.setStaffId(staffId);
                            if (!CollectionUtils.isEmpty(desktop)) {
                                TenantModuleContractDetail desktopTmcd = new TenantModuleContractDetail();
                                desktopTmcd.setSamcId(109000000100001L);
                                desktopTmcd.setAvailableCount(IntegerUtil.objectToInteger(desktop.get("total")));
                                tmcdList.add(desktopTmcd);
                            }
                            if (!CollectionUtils.isEmpty(server)) {
                                TenantModuleContractDetail serverTmcd = new TenantModuleContractDetail();
                                serverTmcd.setSamcId(109000000100002L);
                                serverTmcd.setAvailableCount(IntegerUtil.objectToInteger(server.get("total")));
                                tmcdList.add(serverTmcd);
                            }
                            tmc.setTenantModuleContractDetailList(tmcdList);
                            saveTenantModuleContract(tmc, userName, false);
                            log.info("[Fix Tmc] tmc:{} , tenantTp:{}", tmc, tenantTp);
                        }
                        log.info("[Fix downloadUrl] tenantTp:{}", tp);
                        fixDownloadUrl(tp);

                    }
                });

        return null;
    }

    @Override
    public void fixDownloadUrl(TenantTp tenantTp) {
        CompletableFuture.runAsync(() -> {
            try {
                // 尝试获取许可，如果无法获取则等待
                asiaInfoSemaphore.acquire();
                try {
                    String downloadUrl = getDownloadUrl(tenantTp.getTpTenantId());
                    if (StringUtils.isNotBlank(downloadUrl)) {
                        tenantTp.setTpAgentDownloadUrl(downloadUrl);
                        if (StringUtils.isBlank(tenantTp.getTpProgram())) {
                            tenantTp.setTpProgram(ASIA_EXE);
                        }
                        tenantDao.updateTenantTp(tenantTp);
                    }
                    log.info("[fixDownloadUrl] downloadUrl : {} tenantTp:{}", downloadUrl, JSONObject.toJSONString(tenantTp));
                } finally {
                    // 释放许可
                    asiaInfoSemaphore.release();
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("Thread was interrupted", e);
            }
        }, asiaInfoExecutor);
    }


    private String getDownloadUrl(String uuid) {
        String apiUrl = asiaInfoUrl + "/umc/api/v1/tenant/get_agent_download_url/" + uuid;
        HttpEntity request = AsiaHeaderUtil.buildHeader(apiUrl, false, asiaInfoAccessId, asiaInfoAppSecret, null, asiaInfoUrl);
        RestTemplate restTemplate = new RestTemplate();
        ResponseEntity<AsiaInfoResponse> asiaInfoResponse = restTemplate.exchange(apiUrl, HttpMethod.GET, request, AsiaInfoResponse.class);
        if (AsiaInfoResponse.isSuccess(asiaInfoResponse) && AsiaInfoResponse.dataExists(asiaInfoResponse.getBody())) {
            return asiaInfoResponse.getBody().getData().get("windows_download_url").toString();
        }
        log.error("[getDownloadUrl] error  url : {} , request : {} , res: {}", apiUrl, JSONObject.toJSONString(request), JSONObject.toJSONString(asiaInfoResponse));
        return null;
    }

    @Override
    public List<SubscribeTenant> getSubscribeTenantList(SubscribeTenantReq subscribeTenantReq) {
        int start = (subscribeTenantReq.getPage() - 1) * subscribeTenantReq.getSize();
        subscribeTenantReq.setStart(start);
        subscribeTenantReq.setUserSid(RequestUtil.getHeaderUserSid());
        subscribeTenantReq.setSid(RequestUtil.getHeaderSid());
        List<SubscribeTenant> subscribeTenantList = tenantDao.getSubscribeTenantList(subscribeTenantReq);
        List<Long> eidList = subscribeTenantList.stream().map(o -> o.getSid()).collect(Collectors.toList());
        List<Integer> moduleStatusList = subscribeTenantReq.getModuleStatusList();
        List<String> deviceTypeList = subscribeTenantReq.getDeviceTypeList();
        TenantModuleContractByStatusReq tenantModuleContractByStatusReq = new TenantModuleContractByStatusReq(eidList, moduleStatusList, RequestUtil.getHeaderSid());
        List<Map<Long, Integer>> tmcCnt = tenantDao.getTenantModuleContractCntByModuleStatus(tenantModuleContractByStatusReq);
        TenantDeviceCntReq tenantDeviceCntReq = new TenantDeviceCntReq(eidList, deviceTypeList);
        BaseResponse<List<TenantDeviceCnt>> response = aioItmsFeignClient.getTenantDeviceCnt(tenantDeviceCntReq);
        List<TenantDeviceCnt> allTenantDeviceCntList = new ArrayList<>();
        if (response.checkIsSuccess()) {
            allTenantDeviceCntList = response.getData();
        }
        List<Map<Long, Integer>> userCnt = userDao.getActivateUserCnt(InviteType.ACTIVATE.toString(), eidList);
        for (SubscribeTenant subscribeTenant : subscribeTenantList) {
            long eid = subscribeTenant.getSid();
            tmcCnt.stream().filter(o -> eid == LongUtil.objectToLong(o.get("eid"))).findAny().ifPresent(map -> {
                subscribeTenant.setModuleContractsCnt(IntegerUtil.objectToInteger(map.get("cnt")));
            });
            userCnt.stream().filter(o -> eid == LongUtil.objectToLong(o.get("eid"))).findAny().ifPresent(map -> {
                subscribeTenant.setActivateNoticeUserCnt(IntegerUtil.objectToInteger(map.get("cnt")));
            });
            List<TenantDeviceCnt> tenantDeviceCntList = allTenantDeviceCntList.stream()
                    .filter(o -> eid == o.getEid())
                    .collect(Collectors.toList());
            subscribeTenant.setTenantDeviceCntList(tenantDeviceCntList);
        }
        return subscribeTenantList;
    }

    @Override
    public int getSubscribeTenantCnt(SubscribeTenantReq subscribeTenantReq) {
        subscribeTenantReq.setUserSid(RequestUtil.getHeaderUserSid());
        subscribeTenantReq.setSid(RequestUtil.getHeaderSid());
        return tenantDao.getSubscribeTenantCnt(subscribeTenantReq);
    }

    @Override
    public boolean updateTenantInstalledStatus(boolean installed, long tenantSid) {
        return tenantDao.updateTenantInstalledStatus(installed, tenantSid) > 0;
    }

    @Override
    public List<TenantModuleContract> getTenantModuleContractByModuleStatus(TenantModuleContractByStatusReq tenantModuleContractByStatusReq) {
        tenantModuleContractByStatusReq.setSid(RequestUtil.getHeaderSid());
        return tenantDao.getTenantModuleContractByModuleStatus(tenantModuleContractByStatusReq);
    }

    @Override
    public ResponseBase updateTCProductId(long eid, String productId, String productCode) {
        long sid = RequestUtil.getHeaderSid();
        tenantDao.updateTCProductIdByProductCode(sid, eid, productCode, productId);
        return ResponseBase.ok();
    }
}
